"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/book/page",{

/***/ "(app-pages-browser)/./components/appointment-form.tsx":
/*!*****************************************!*\
  !*** ./components/appointment-form.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentForm: () => (/* binding */ AppointmentForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/hydration-safe-input */ \"(app-pages-browser)/./components/ui/hydration-safe-input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AppointmentForm(param) {\n    let { onSuccess } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Basic Information\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        // Appointment Details\n        service: \"\",\n        preferred_date: \"\",\n        preferred_time: \"\",\n        message: \"\",\n        // Medical History\n        medical_history: \"\",\n        allergies: \"\",\n        current_medications: \"\",\n        previous_dental_work: \"\",\n        dental_concerns: \"\",\n        // Emergency Contact\n        emergency_contact_name: \"\",\n        emergency_contact_phone: \"\",\n        emergency_contact_relationship: \"\",\n        // Insurance Information (Optional)\n        has_insurance: \"\",\n        insurance_provider: \"\",\n        insurance_policy_number: \"\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const [availableTimeSlots, setAvailableTimeSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingSlots, setLoadingSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch available time slots when date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (formData.preferred_date) {\n                fetchAvailableSlots(formData.preferred_date);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        formData.preferred_date\n    ]);\n    const fetchAvailableSlots = async (date)=>{\n        setLoadingSlots(true);\n        try {\n            const response = await fetch(\"/api/availability?date=\".concat(date));\n            const data = await response.json();\n            if (data.available && data.timeSlots) {\n                // Extract only available time slots\n                const slots = data.timeSlots.filter((slot)=>{\n                    const currentBookings = slot.current_appointments || 0;\n                    return currentBookings < slot.max_appointments;\n                }).map((slot)=>slot.time);\n                setAvailableTimeSlots(slots);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        } catch (error) {\n            console.error('Error fetching available slots:', error);\n            setAvailableTimeSlots([]);\n        } finally{\n            setLoadingSlots(false);\n        }\n    };\n    const services = [\n        \"✨ The Signature Glow\",\n        \"⚡ Express Refresh\",\n        \"💎 Complete Smile Makeover\",\n        \"🦷 Clear Aligners (Premium)\",\n        \"🔧 Root Canal Treatment\",\n        \"👑 Dental Crowns\",\n        \"🧽 Professional Cleaning\",\n        \"🦷 Dental Implants\",\n        \"Other (Please specify in message)\"\n    ];\n    const timeSlots = [\n        \"9:00 AM\",\n        \"10:00 AM\",\n        \"11:00 AM\",\n        \"12:00 PM\",\n        \"2:00 PM\",\n        \"3:00 PM\",\n        \"4:00 PM\",\n        \"5:00 PM\",\n        \"6:00 PM\",\n        \"7:00 PM\",\n        \"8:00 PM\"\n    ];\n    const handleChange = (e)=>{\n        const { id, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [id]: value\n            }));\n    };\n    const handleSelectChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.name && formData.email && formData.phone && formData.date_of_birth;\n            case 2:\n                return formData.service && formData.preferred_date && formData.preferred_time;\n            case 3:\n                return formData.emergency_contact_name && formData.emergency_contact_phone;\n            case 4:\n                return true // Optional step\n                ;\n            default:\n                return false;\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            const response = await fetch('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setSubmitStatus('success');\n                setFormData({\n                    // Basic Information\n                    name: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    date_of_birth: \"\",\n                    address: \"\",\n                    // Appointment Details\n                    service: \"\",\n                    preferred_date: \"\",\n                    preferred_time: \"\",\n                    message: \"\",\n                    // Medical History\n                    medical_history: \"\",\n                    allergies: \"\",\n                    current_medications: \"\",\n                    previous_dental_work: \"\",\n                    dental_concerns: \"\",\n                    // Emergency Contact\n                    emergency_contact_name: \"\",\n                    emergency_contact_phone: \"\",\n                    emergency_contact_relationship: \"\",\n                    // Insurance Information (Optional)\n                    has_insurance: \"\",\n                    insurance_provider: \"\",\n                    insurance_policy_number: \"\"\n                });\n                setCurrentStep(1); // Reset to first step\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                setSubmitStatus('error');\n                console.error('Appointment booking error:', result.message);\n            }\n        } catch (error) {\n            setSubmitStatus('error');\n            console.error('Network error:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Get today's date in YYYY-MM-DD format for min date\n    const today = new Date().toISOString().split('T')[0];\n    const stepTitles = [\n        \"Personal Information\",\n        \"Appointment Details\",\n        \"Medical History & Emergency Contact\",\n        \"Insurance Information (Optional)\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: [\n                                    \"Step \",\n                                    currentStep,\n                                    \" of \",\n                                    totalSteps,\n                                    \": \",\n                                    stepTitles[currentStep - 1]\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    Math.round(currentStep / totalSteps * 100),\n                                    \"% Complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#6366F1] to-[#F59E0B] h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-green-50 border border-green-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-800 font-medium\",\n                    children: \"\\uD83C\\uDF89 Appointment booked successfully! We will call you within 2 hours to confirm your appointment.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this),\n            submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border border-red-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Sorry, there was an error booking your appointment. Please try again or call us directly at +91-11-41234567.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this),\n            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"name\",\n                                        className: \"text-foreground\",\n                                        children: \"Full Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"name\",\n                                        type: \"text\",\n                                        placeholder: \"Your Full Name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"email\",\n                                        className: \"text-foreground\",\n                                        children: \"Email *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        placeholder: \"<EMAIL>\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"phone\",\n                                        className: \"text-foreground\",\n                                        children: \"Phone Number *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"phone\",\n                                        type: \"tel\",\n                                        placeholder: \"+91-XXXXXXXXXX\",\n                                        value: formData.phone,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"date_of_birth\",\n                                        className: \"text-foreground\",\n                                        children: \"Date of Birth *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"date_of_birth\",\n                                        type: \"date\",\n                                        value: formData.date_of_birth,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"address\",\n                                className: \"text-foreground\",\n                                children: \"Address\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"address\",\n                                placeholder: \"Your complete address\",\n                                value: formData.address,\n                                onChange: handleChange,\n                                className: \"mt-1\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this),\n            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"service\",\n                                className: \"text-foreground\",\n                                children: \"Service Required *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                onValueChange: (value)=>handleSelectChange('service', value),\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Select a service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: service,\n                                                children: service\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_date\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Date *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"preferred_date\",\n                                        type: \"date\",\n                                        min: today,\n                                        value: formData.preferred_date,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_time\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Time *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        onValueChange: (value)=>handleSelectChange('preferred_time', value),\n                                        disabled: isSubmitting || loadingSlots || !formData.preferred_date,\n                                        value: formData.preferred_time,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: !formData.preferred_date ? \"Please select a date first\" : loadingSlots ? \"Loading available times...\" : availableTimeSlots.length === 0 ? \"No availability for this date\" : \"Select time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: availableTimeSlots.map((time)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: time,\n                                                        children: time\n                                                    }, time, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.preferred_date && !loadingSlots && availableTimeSlots.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600 mt-1\",\n                                        children: \"No availability for this date. Please select a different date.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"message\",\n                                className: \"text-foreground\",\n                                children: \"Additional Message\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"message\",\n                                placeholder: \"Any specific concerns or requirements? (Optional)\",\n                                value: formData.message,\n                                onChange: handleChange,\n                                className: \"mt-1 min-h-[100px]\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this),\n            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Medical History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"medical_history\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"medical_history\",\n                                        placeholder: \"Please list any medical conditions, surgeries, or ongoing treatments\",\n                                        value: formData.medical_history,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"allergies\",\n                                        className: \"text-foreground\",\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"allergies\",\n                                        placeholder: \"Please list any allergies (medications, foods, materials, etc.)\",\n                                        value: formData.allergies,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"current_medications\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"current_medications\",\n                                        placeholder: \"Please list all medications you are currently taking\",\n                                        value: formData.current_medications,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"previous_dental_work\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Dental Work\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"previous_dental_work\",\n                                        placeholder: \"Please describe any previous dental treatments or procedures\",\n                                        value: formData.previous_dental_work,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"dental_concerns\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Dental Concerns\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"dental_concerns\",\n                                        placeholder: \"Please describe any current dental pain, concerns, or symptoms\",\n                                        value: formData.dental_concerns,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Emergency Contact *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_name\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_name\",\n                                                type: \"text\",\n                                                placeholder: \"Emergency contact full name\",\n                                                value: formData.emergency_contact_name,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_phone\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Phone *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_phone\",\n                                                type: \"tel\",\n                                                placeholder: \"+91-XXXXXXXXXX\",\n                                                value: formData.emergency_contact_phone,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"emergency_contact_relationship\",\n                                        className: \"text-foreground\",\n                                        children: \"Relationship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"emergency_contact_relationship\",\n                                        type: \"text\",\n                                        placeholder: \"e.g., Spouse, Parent, Sibling, Friend\",\n                                        value: formData.emergency_contact_relationship,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, this),\n            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-foreground\",\n                            children: \"Insurance Information (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"This information helps us process your treatment more efficiently, but it's completely optional.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"has_insurance\",\n                                    className: \"text-foreground\",\n                                    children: \"Do you have dental insurance?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: (value)=>handleSelectChange('has_insurance', value),\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                placeholder: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"yes\",\n                                                    children: \"Yes, I have dental insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"no\",\n                                                    children: \"No, I don't have insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"unsure\",\n                                                    children: \"I'm not sure\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this),\n                        formData.has_insurance === 'yes' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_provider\",\n                                            className: \"text-foreground\",\n                                            children: \"Insurance Provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_provider\",\n                                            type: \"text\",\n                                            placeholder: \"e.g., Star Health, HDFC ERGO, etc.\",\n                                            value: formData.insurance_provider,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_policy_number\",\n                                            className: \"text-foreground\",\n                                            children: \"Policy Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_policy_number\",\n                                            type: \"text\",\n                                            placeholder: \"Your insurance policy number\",\n                                            value: formData.insurance_policy_number,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6\",\n                children: [\n                    currentStep > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: prevStep,\n                        disabled: isSubmitting,\n                        className: \"px-6\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto\",\n                        children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            onClick: nextStep,\n                            disabled: !isStepValid(currentStep) || isSubmitting,\n                            className: \"px-6 bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white\",\n                            children: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            disabled: isSubmitting || !isStepValid(currentStep),\n                            className: \"px-8 rounded-2xl bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white font-semibold py-3 text-lg shadow-lg hover:from-[#5a5ee0] hover:to-[#e08d0a] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Booking Appointment...\"\n                                ]\n                            }, void 0, true) : 'Book Appointment'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 629,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-muted-foreground text-center\",\n                children: [\n                    \"* We will call you within 2 hours to confirm your appointment. For urgent needs, call us directly at\",\n                    ' ',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+911141234567\",\n                        className: \"text-primary hover:underline\",\n                        children: \"+91-11-41234567\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 671,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentForm, \"Yzw6WW5nxYcVlkDjUXzjiEoj0ok=\");\n_c = AppointmentForm;\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/appointment-form.tsx\n"));

/***/ })

});