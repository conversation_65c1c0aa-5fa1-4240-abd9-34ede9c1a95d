"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/book/page",{

/***/ "(app-pages-browser)/./components/appointment-form.tsx":
/*!*****************************************!*\
  !*** ./components/appointment-form.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentForm: () => (/* binding */ AppointmentForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/hydration-safe-input */ \"(app-pages-browser)/./components/ui/hydration-safe-input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AppointmentForm(param) {\n    let { onSuccess } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Basic Information\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        // Appointment Details\n        service: \"\",\n        preferred_date: \"\",\n        preferred_time: \"\",\n        message: \"\",\n        // Medical History\n        medical_history: \"\",\n        allergies: \"\",\n        current_medications: \"\",\n        previous_dental_work: \"\",\n        dental_concerns: \"\",\n        // Emergency Contact\n        emergency_contact_name: \"\",\n        emergency_contact_phone: \"\",\n        emergency_contact_relationship: \"\",\n        // Insurance Information (Optional)\n        has_insurance: \"\",\n        insurance_provider: \"\",\n        insurance_policy_number: \"\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const [availableTimeSlots, setAvailableTimeSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTimeSlot, setSelectedTimeSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSlots, setLoadingSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Helper function to format time\n    const formatTime = (time)=>{\n        const [hours, minutes] = time.split(':');\n        const hour = parseInt(hours);\n        const ampm = hour >= 12 ? 'PM' : 'AM';\n        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;\n        return \"\".concat(displayHour, \":\").concat(minutes, \" \").concat(ampm);\n    };\n    // Fetch available time slots when date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (formData.preferred_date) {\n                fetchAvailableSlots(formData.preferred_date);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        formData.preferred_date\n    ]);\n    const fetchAvailableSlots = async (date)=>{\n        setLoadingSlots(true);\n        try {\n            const response = await fetch(\"/api/availability?date=\".concat(date));\n            const data = await response.json();\n            if (data.available && data.timeSlots) {\n                // Transform time slots to include availability information\n                const slots = data.timeSlots.filter((slot)=>{\n                    const currentBookings = slot.current_appointments || 0;\n                    return currentBookings < slot.max_appointments;\n                }).map((slot)=>({\n                        time: slot.time,\n                        maxAppointments: slot.max_appointments,\n                        currentAppointments: slot.current_appointments || 0,\n                        availableSpots: slot.max_appointments - (slot.current_appointments || 0)\n                    }));\n                setAvailableTimeSlots(slots);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        } catch (error) {\n            console.error('Error fetching available slots:', error);\n            setAvailableTimeSlots([]);\n        } finally{\n            setLoadingSlots(false);\n        }\n    };\n    const services = [\n        \"✨ The Signature Glow\",\n        \"⚡ Express Refresh\",\n        \"💎 Complete Smile Makeover\",\n        \"🦷 Clear Aligners (Premium)\",\n        \"🔧 Root Canal Treatment\",\n        \"👑 Dental Crowns\",\n        \"🧽 Professional Cleaning\",\n        \"🦷 Dental Implants\",\n        \"Other (Please specify in message)\"\n    ];\n    const timeSlots = [\n        \"9:00 AM\",\n        \"10:00 AM\",\n        \"11:00 AM\",\n        \"12:00 PM\",\n        \"2:00 PM\",\n        \"3:00 PM\",\n        \"4:00 PM\",\n        \"5:00 PM\",\n        \"6:00 PM\",\n        \"7:00 PM\",\n        \"8:00 PM\"\n    ];\n    const handleChange = (e)=>{\n        const { id, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [id]: value\n            }));\n    };\n    const handleSelectChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // If selecting preferred_time, also store the slot details\n        if (field === 'preferred_time') {\n            const slot = availableTimeSlots.find((s)=>s.time === value);\n            setSelectedTimeSlot(slot || null);\n        }\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.name && formData.email && formData.phone && formData.date_of_birth;\n            case 2:\n                return formData.service && formData.preferred_date && formData.preferred_time;\n            case 3:\n                return formData.emergency_contact_name && formData.emergency_contact_phone;\n            case 4:\n                return true // Optional step\n                ;\n            default:\n                return false;\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            const response = await fetch('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setSubmitStatus('success');\n                setFormData({\n                    // Basic Information\n                    name: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    date_of_birth: \"\",\n                    address: \"\",\n                    // Appointment Details\n                    service: \"\",\n                    preferred_date: \"\",\n                    preferred_time: \"\",\n                    message: \"\",\n                    // Medical History\n                    medical_history: \"\",\n                    allergies: \"\",\n                    current_medications: \"\",\n                    previous_dental_work: \"\",\n                    dental_concerns: \"\",\n                    // Emergency Contact\n                    emergency_contact_name: \"\",\n                    emergency_contact_phone: \"\",\n                    emergency_contact_relationship: \"\",\n                    // Insurance Information (Optional)\n                    has_insurance: \"\",\n                    insurance_provider: \"\",\n                    insurance_policy_number: \"\"\n                });\n                setCurrentStep(1); // Reset to first step\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                setSubmitStatus('error');\n                console.error('Appointment booking error:', result.message);\n            }\n        } catch (error) {\n            setSubmitStatus('error');\n            console.error('Network error:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Get today's date in YYYY-MM-DD format for min date\n    const today = new Date().toISOString().split('T')[0];\n    const stepTitles = [\n        \"Personal Information\",\n        \"Appointment Details\",\n        \"Medical History & Emergency Contact\",\n        \"Insurance Information (Optional)\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: [\n                                    \"Step \",\n                                    currentStep,\n                                    \" of \",\n                                    totalSteps,\n                                    \": \",\n                                    stepTitles[currentStep - 1]\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    Math.round(currentStep / totalSteps * 100),\n                                    \"% Complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#6366F1] to-[#F59E0B] h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-green-50 border border-green-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-800 font-medium\",\n                    children: \"\\uD83C\\uDF89 Appointment booked successfully! We will call you within 2 hours to confirm your appointment.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this),\n            submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border border-red-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Sorry, there was an error booking your appointment. Please try again or call us directly at +91-11-41234567.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this),\n            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"name\",\n                                        className: \"text-foreground\",\n                                        children: \"Full Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"name\",\n                                        type: \"text\",\n                                        placeholder: \"Your Full Name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"email\",\n                                        className: \"text-foreground\",\n                                        children: \"Email *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        placeholder: \"<EMAIL>\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"phone\",\n                                        className: \"text-foreground\",\n                                        children: \"Phone Number *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"phone\",\n                                        type: \"tel\",\n                                        placeholder: \"+91-XXXXXXXXXX\",\n                                        value: formData.phone,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"date_of_birth\",\n                                        className: \"text-foreground\",\n                                        children: \"Date of Birth *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"date_of_birth\",\n                                        type: \"date\",\n                                        value: formData.date_of_birth,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"address\",\n                                className: \"text-foreground\",\n                                children: \"Address\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"address\",\n                                placeholder: \"Your complete address\",\n                                value: formData.address,\n                                onChange: handleChange,\n                                className: \"mt-1\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this),\n            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"service\",\n                                className: \"text-foreground\",\n                                children: \"Service Required *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                onValueChange: (value)=>handleSelectChange('service', value),\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Select a service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: service,\n                                                children: service\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_date\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Date *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"preferred_date\",\n                                        type: \"date\",\n                                        min: today,\n                                        value: formData.preferred_date,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_time\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Time *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        onValueChange: (value)=>handleSelectChange('preferred_time', value),\n                                        disabled: isSubmitting || loadingSlots || !formData.preferred_date,\n                                        value: formData.preferred_time,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: !formData.preferred_date ? \"Please select a date first\" : loadingSlots ? \"Loading available times...\" : availableTimeSlots.length === 0 ? \"No availability for this date\" : \"Select time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: availableTimeSlots.map((slot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: slot.time,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatTime(slot.time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground ml-2\",\n                                                                    children: [\n                                                                        slot.availableSpots,\n                                                                        \" spot\",\n                                                                        slot.availableSpots !== 1 ? 's' : '',\n                                                                        \" available\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, slot.time, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.preferred_date && !loadingSlots && availableTimeSlots.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600 mt-1\",\n                                        children: \"No availability for this date. Please select a different date.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"message\",\n                                className: \"text-foreground\",\n                                children: \"Additional Message\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"message\",\n                                placeholder: \"Any specific concerns or requirements? (Optional)\",\n                                value: formData.message,\n                                onChange: handleChange,\n                                className: \"mt-1 min-h-[100px]\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this),\n            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Medical History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"medical_history\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"medical_history\",\n                                        placeholder: \"Please list any medical conditions, surgeries, or ongoing treatments\",\n                                        value: formData.medical_history,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"allergies\",\n                                        className: \"text-foreground\",\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"allergies\",\n                                        placeholder: \"Please list any allergies (medications, foods, materials, etc.)\",\n                                        value: formData.allergies,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"current_medications\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"current_medications\",\n                                        placeholder: \"Please list all medications you are currently taking\",\n                                        value: formData.current_medications,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"previous_dental_work\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Dental Work\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"previous_dental_work\",\n                                        placeholder: \"Please describe any previous dental treatments or procedures\",\n                                        value: formData.previous_dental_work,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"dental_concerns\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Dental Concerns\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"dental_concerns\",\n                                        placeholder: \"Please describe any current dental pain, concerns, or symptoms\",\n                                        value: formData.dental_concerns,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Emergency Contact *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_name\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_name\",\n                                                type: \"text\",\n                                                placeholder: \"Emergency contact full name\",\n                                                value: formData.emergency_contact_name,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_phone\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Phone *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_phone\",\n                                                type: \"tel\",\n                                                placeholder: \"+91-XXXXXXXXXX\",\n                                                value: formData.emergency_contact_phone,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"emergency_contact_relationship\",\n                                        className: \"text-foreground\",\n                                        children: \"Relationship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"emergency_contact_relationship\",\n                                        type: \"text\",\n                                        placeholder: \"e.g., Spouse, Parent, Sibling, Friend\",\n                                        value: formData.emergency_contact_relationship,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 474,\n                columnNumber: 9\n            }, this),\n            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-foreground\",\n                            children: \"Insurance Information (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"This information helps us process your treatment more efficiently, but it's completely optional.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"has_insurance\",\n                                    className: \"text-foreground\",\n                                    children: \"Do you have dental insurance?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: (value)=>handleSelectChange('has_insurance', value),\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                placeholder: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"yes\",\n                                                    children: \"Yes, I have dental insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"no\",\n                                                    children: \"No, I don't have insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"unsure\",\n                                                    children: \"I'm not sure\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 13\n                        }, this),\n                        formData.has_insurance === 'yes' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_provider\",\n                                            className: \"text-foreground\",\n                                            children: \"Insurance Provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_provider\",\n                                            type: \"text\",\n                                            placeholder: \"e.g., Star Health, HDFC ERGO, etc.\",\n                                            value: formData.insurance_provider,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_policy_number\",\n                                            className: \"text-foreground\",\n                                            children: \"Policy Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_policy_number\",\n                                            type: \"text\",\n                                            placeholder: \"Your insurance policy number\",\n                                            value: formData.insurance_policy_number,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 606,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6\",\n                children: [\n                    currentStep > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: prevStep,\n                        disabled: isSubmitting,\n                        className: \"px-6\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto\",\n                        children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            onClick: nextStep,\n                            disabled: !isStepValid(currentStep) || isSubmitting,\n                            className: \"px-6 bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white\",\n                            children: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            disabled: isSubmitting || !isStepValid(currentStep),\n                            className: \"px-8 rounded-2xl bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white font-semibold py-3 text-lg shadow-lg hover:from-[#5a5ee0] hover:to-[#e08d0a] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Booking Appointment...\"\n                                ]\n                            }, void 0, true) : 'Book Appointment'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-muted-foreground text-center\",\n                children: [\n                    \"* We will call you within 2 hours to confirm your appointment. For urgent needs, call us directly at\",\n                    ' ',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+911141234567\",\n                        className: \"text-primary hover:underline\",\n                        children: \"+91-11-41234567\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 709,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentForm, \"QAojgR/qBUp3v9H/PIaqF+OnFJw=\");\n_c = AppointmentForm;\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/appointment-form.tsx\n"));

/***/ })

});