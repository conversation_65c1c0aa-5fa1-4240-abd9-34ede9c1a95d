{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/%40radix-ui/number/src/number.ts"], "sourcesContent": ["function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,MAAM,KAAA,OAAwB;SAAR,KAAK,GAAG,CAAA,EAA6B,CAAtC;IAC5B,OAAO,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,KAAK,CAAC;AAC3C", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/%40radix-ui/react-use-previous/src/use-previous.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAEvB,SAAS,YAAe,KAAA,EAAU;IAChC,MAAM,MAAY,uKAAA,CAAO;QAAE;QAAO,UAAU;IAAM,CAAC;IAKnD,OAAa,wKAAA;+BAAQ,MAAM;YACzB,IAAI,IAAI,OAAA,CAAQ,KAAA,KAAU,OAAO;gBAC/B,IAAI,OAAA,CAAQ,QAAA,GAAW,IAAI,OAAA,CAAQ,KAAA;gBACnC,IAAI,OAAA,CAAQ,KAAA,GAAQ;YACtB;YACA,OAAO,IAAI,OAAA,CAAQ,QAAA;QACrB;8BAAG;QAAC,KAAK;KAAC;AACZ", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/%40radix-ui/react-visually-hidden/src/visually-hidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: 'absolute',\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: 'hidden',\n  clip: 'rect(0, 0, 0, 0)',\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}) satisfies React.CSSProperties;\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n  //\n  VISUALLY_HIDDEN_STYLES,\n};\nexport type { VisuallyHiddenProps };\n"], "names": [], "mappings": ";;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AA6BpB;;;;AAvBN,IAAM,yBAAyB,OAAO,MAAA,CAAO;IAAA,qFAAA;IAE3C,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ,CAAA;IACR,UAAU;IACV,MAAM;IACN,YAAY;IACZ,UAAU;AACZ,CAAC;AAED,IAAM,OAAO;AAMb,IAAM,iBAAuB,2KAAA,CAC3B,CAAC,OAAO,iBAAiB;IACvB,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,IAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,OAAO;YAAE,GAAG,sBAAA;YAAwB,GAAG,MAAM,KAAA;QAAM;IAAA;AAGzD;AAGF,eAAe,WAAA,GAAc;AAI7B,IAAM,OAAO", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/%40radix-ui/react-select/src/select.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n"], "names": ["handleScroll", "canScrollUp", "canScrollDown", "Root", "Content", "Arrow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,aAAa;AACtB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AACjC,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,aAAa;AACtB,YAAY,qBAAqB;AAEjC,SAAS,UAAU,uBAAuB;AAC1C,SAAS,iBAAiB;AAC1B,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,mBAAmB;AAC5B,SAAS,8BAA8B;AACvC,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAiLnB,SA0LsC,UA1LtC,KAkBA,YAlBA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3KV,IAAM,YAAY;IAAC;IAAK;IAAS;IAAW,WAAW;CAAA;AACvD,IAAM,iBAAiB;IAAC;IAAK,OAAO;CAAA;AAMpC,IAAM,cAAc;AAGpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,OAAI,6LAAA,EAGzD,WAAW;AAGb,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,OAAI,4LAAA,EAAmB,aAAa;IAC/E;IACA,0LAAA;CACD;AACD,IAAM,qBAAiB,0LAAA,CAAkB;AAoBzC,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAQ9F,IAAM,CAAC,6BAA6B,6BAA6B,CAAA,GAC/D,oBAAqD,WAAW;AAoDlE,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EACJ,aAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,OAAO,SAAA,EACP,YAAA,EACA,aAAA,EACA,GAAA,EACA,IAAA,EACA,YAAA,EACA,QAAA,EACA,QAAA,EACA,IAAA,EACF,GAAI;IACJ,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,yKAAA,CAAsC,IAAI;IAC9E,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,yKAAA,CAAoC,IAAI;IAChF,MAAM,CAAC,sBAAsB,uBAAuB,CAAA,GAAU,yKAAA,CAAS,KAAK;IAC5E,MAAM,gBAAY,wLAAA,EAAa,GAAG;IAClC,MAAM,CAAC,MAAM,OAAO,CAAA,OAAI,mNAAA,EAAqB;QAC3C,MAAM;QACN,8DAAa,cAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAI,uNAAA,EAAqB;QAC7C,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,2BAAiC,uKAAA,CAAwC,IAAI;IAGnF,MAAM,gBAAgB,UAAU,QAAQ,CAAC,CAAC,QAAQ,OAAA,CAAQ,MAAM,IAAI;IACpE,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAU,yKAAA,CAAS,aAAA,GAAA,IAAI,IAAkB,CAAC;IAOtF,MAAM,kBAAkB,MAAM,IAAA,CAAK,gBAAgB,EAChD,GAAA,CAAI,CAAC,SAAW,OAAO,KAAA,CAAM,KAAK,EAClC,IAAA,CAAK,GAAG;IAEX,OACE,aAAA,GAAA,IAAA,6KAAA,EAAiB,6KAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,IAAA,8KAAA,EAAC,gBAAA;YACC;YACA,OAAO;YACP;YACA,iBAAiB;YACjB;YACA,mBAAmB;YACnB;YACA,8BAA8B;YAC9B,eAAW,0KAAA,CAAM;YACjB;YACA,eAAe;YACf;YACA,cAAc;YACd,KAAK;YACL;YACA;YAEA,UAAA;gBAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,WAAW,QAAA,EAAX;oBAAoB,OAAO;oBAC1B,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,6BAAA;wBACC,OAAO,MAAM,aAAA;wBACb,mBAAyB,4KAAA;kDAAY,CAAC,WAAW;gCAC/C;0DAAoB,CAAC,OAAS,IAAI,IAAI,IAAI,EAAE,GAAA,CAAI,MAAM,CAAC;;4BACzD;iDAAG,CAAC,CAAC;wBACL,sBAA4B,4KAAA;kDAAY,CAAC,WAAW;gCAClD;0DAAoB,CAAC,SAAS;wCAC5B,MAAM,aAAa,IAAI,IAAI,IAAI;wCAC/B,WAAW,MAAA,CAAO,MAAM;wCACxB,OAAO;oCACT,CAAC;;4BACH;iDAAG,CAAC,CAAC;wBAEJ;oBAAA;gBACH,CACF;gBAEC,gBACC,aAAA,GAAA,IAAA,8KAAA,EAAC,mBAAA;oBAEC,eAAW;oBACX;oBACA,UAAU,CAAA;oBACV;oBACA;oBACA;oBAEA,UAAU,CAAC,QAAU,SAAS,MAAM,MAAA,CAAO,KAAK;oBAChD;oBACA;oBAEC,UAAA;wBAAA,UAAU,KAAA,IAAY,aAAA,GAAA,IAAA,6KAAA,EAAC,UAAA;4BAAO,OAAM;wBAAA,CAAG,IAAK;wBAC5C,MAAM,IAAA,CAAK,gBAAgB;qBAAA;gBAAA,GAbvB,mBAeL;aAAA;QAAA;IACN,CACF;AAEJ;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB,2KAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,aAAA,EAAe,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAC7D,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,aAAa,QAAQ,QAAA,IAAY;IACvC,MAAM,mBAAe,iMAAA,EAAgB,cAAc,QAAQ,eAAe;IAC1E,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,iBAAuB,uKAAA,CAA0C,OAAO;IAE9E,MAAM,CAAC,WAAW,uBAAuB,cAAc,CAAA,GAAI;4CAAmB,CAAC,WAAW;YACxF,MAAM,eAAe,SAAS,EAAE,MAAA;iEAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;;YAC/D,MAAM,cAAc,aAAa,IAAA;gEAAK,CAAC,OAAS,KAAK,KAAA,KAAU,QAAQ,KAAK;;YAC5E,MAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;YAC/D,IAAI,aAAa,KAAA,GAAW;gBAC1B,QAAQ,aAAA,CAAc,SAAS,KAAK;YACtC;QACF,CAAC;;IAED,MAAM,aAAa,CAAC,iBAAyD;QAC3E,IAAI,CAAC,YAAY;YACf,QAAQ,YAAA,CAAa,IAAI;YAEzB,eAAe;QACjB;QAEA,IAAI,cAAc;YAChB,QAAQ,wBAAA,CAAyB,OAAA,GAAU;gBACzC,GAAG,KAAK,KAAA,CAAM,aAAa,KAAK;gBAChC,GAAG,KAAK,KAAA,CAAM,aAAa,KAAK;YAClC;QACF;IACF;IAEA,OACE,aAAA,GAAA,IAAA,6KAAA,EAAiB,+KAAA,EAAhB;QAAuB,SAAO;QAAE,GAAG,WAAA;QAClC,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe,QAAQ,SAAA;YACvB,iBAAe,QAAQ,IAAA;YACvB,iBAAe,QAAQ,QAAA;YACvB,qBAAkB;YAClB,KAAK,QAAQ,GAAA;YACb,cAAY,QAAQ,IAAA,GAAO,SAAS;YACpC,UAAU;YACV,iBAAe,aAAa,KAAK,KAAA;YACjC,oBAAkB,sBAAsB,QAAQ,KAAK,IAAI,KAAK,KAAA;YAC7D,GAAG,YAAA;YACJ,KAAK;YAEL,aAAS,uLAAA,EAAqB,aAAa,OAAA,EAAS,CAAC,UAAU;gBAM7D,MAAM,aAAA,CAAc,KAAA,CAAM;gBAG1B,IAAI,eAAe,OAAA,KAAY,SAAS;oBACtC,WAAW,KAAK;gBAClB;YACF,CAAC;YACD,mBAAe,uLAAA,EAAqB,aAAa,aAAA,EAAe,CAAC,UAAU;gBACzE,eAAe,OAAA,GAAU,MAAM,WAAA;gBAI/B,MAAM,SAAS,MAAM,MAAA;gBACrB,IAAI,OAAO,iBAAA,CAAkB,MAAM,SAAS,GAAG;oBAC7C,OAAO,qBAAA,CAAsB,MAAM,SAAS;gBAC9C;gBAKA,IAAI,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,SAAS,MAAM,WAAA,KAAgB,SAAS;oBAClF,WAAW,KAAK;oBAEhB,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,WAAW,2LAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;gBACjE,MAAM,gBAAgB,UAAU,OAAA,KAAY;gBAC5C,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;gBAC7D,IAAI,CAAC,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW,EAAG,CAAA,sBAAsB,MAAM,GAAG;gBAC7E,IAAI,iBAAiB,MAAM,GAAA,KAAQ,IAAK,CAAA;gBACxC,IAAI,UAAU,QAAA,CAAS,MAAM,GAAG,GAAG;oBACjC,WAAW;oBACX,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,aAAa;AAQnB,IAAM,cAAoB,2KAAA,CACxB,CAAC,OAAsC,iBAAiB;IAEtD,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,KAAA,EAAO,QAAA,EAAU,cAAc,EAAA,EAAI,GAAG,WAAW,CAAA,GAAI;IACvF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,EAAE,4BAAA,CAA6B,CAAA,GAAI;IACzC,MAAM,cAAc,aAAa,KAAA;IACjC,MAAM,mBAAe,iMAAA,EAAgB,cAAc,QAAQ,iBAAiB;IAE5E,IAAA,yMAAA;uCAAgB,MAAM;YACpB,6BAA6B,WAAW;QAC1C;sCAAG;QAAC;QAA8B,WAAW;KAAC;IAE9C,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,IAAA,EAAV;QACE,GAAG,UAAA;QACJ,KAAK;QAGL,OAAO;YAAE,eAAe;QAAO;QAE9B,UAAA,sBAAsB,QAAQ,KAAK,IAAI,aAAA,GAAA,IAAA,6KAAA,EAAA,kLAAA,EAAA;YAAG,UAAA;QAAA,CAAY,IAAM;IAAA;AAGnE;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,YAAY;AAKlB,IAAM,aAAmB,2KAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,aAAA,EAAe,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IAClD,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,IAAA,EAAV;QAAe,eAAW;QAAE,GAAG,SAAA;QAAW,KAAK;QAC7C,UAAA,YAAY;IAAA,CACf;AAEJ;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,cAAc;AAWpB,IAAM,eAA4C,CAAC,UAA0C;IAC3F,OAAO,aAAA,GAAA,IAAA,6KAAA,EAAC,+KAAA,EAAA;QAAgB,SAAO;QAAE,GAAG,KAAA;IAAA,CAAO;AAC7C;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAKrB,IAAM,gBAAsB,2KAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,CAAC,UAAU,WAAW,CAAA,GAAU,yKAAA,CAA2B;IAGjE,IAAA,yMAAA;yCAAgB,MAAM;YACpB,YAAY,IAAI,iBAAiB,CAAC;QACpC;wCAAG,CAAC,CAAC;IAEL,IAAI,CAAC,QAAQ,IAAA,EAAM;QACjB,MAAM,OAAO;QACb,OAAO,OACM,oLAAA,CACP,aAAA,GAAA,IAAA,6KAAA,EAAC,uBAAA;YAAsB,OAAO,MAAM,aAAA;YAClC,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,aAAA;gBAC5B,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,OAAA;oBAAK,UAAA,MAAM,QAAA;gBAAA,CAAS;YAAA,CACvB;QAAA,CACF,GACA,QAEF;IACN;IAEA,OAAO,aAAA,GAAA,IAAA,6KAAA,EAAC,mBAAA;QAAmB,GAAG,KAAA;QAAO,KAAK;IAAA,CAAc;AAC1D;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,iBAAiB;AAqBvB,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GACnD,oBAA+C,YAAY;AAE7D,IAAM,oBAAoB;AA8B1B,IAAM,OAAO,qLAAA,EAAW,4BAA4B;AAEpD,IAAM,oBAA0B,2KAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EACJ,aAAA,EACA,WAAW,cAAA,EACX,gBAAA,EACA,eAAA,EACA,oBAAA,EAAA,EAAA;IAAA,sBAAA;IAGA,IAAA,EACA,UAAA,EACA,KAAA,EACA,WAAA,EACA,YAAA,EACA,iBAAA,EACA,gBAAA,EACA,MAAA,EACA,gBAAA,EACA,eAAA,EAAA,EAAA;IAEA,GAAG,cACL,GAAI;IACJ,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,yKAAA,CAA0C,IAAI;IAClF,MAAM,CAAC,UAAU,WAAW,CAAA,GAAU,yKAAA,CAAuC,IAAI;IACjF,MAAM,mBAAe,iMAAA,EAAgB;2DAAc,CAAC,OAAS,WAAW,IAAI,CAAC;;IAC7E,MAAM,CAAC,cAAc,eAAe,CAAA,GAAU,yKAAA,CAAmC,IAAI;IACrF,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAU,yKAAA,CACpD;IAEF,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,CAAC,cAAc,eAAe,CAAA,GAAU,yKAAA,CAAS,KAAK;IAC5D,MAAM,yBAA+B,uKAAA,CAAO,KAAK;IAG3C,0KAAA;uCAAU,MAAM;YACpB,IAAI,QAAS,CAAA,WAAO,0KAAA,EAAW,OAAO;QACxC;sCAAG;QAAC,OAAO;KAAC;IAIZ,IAAA,gMAAA,CAAe;IAEf,MAAM,aAAmB,4KAAA;qDACvB,CAAC,eAA0C;YACzC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAA,GAAI,SAAS,EAAE,GAAA;6DAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAO;;YAC3E,MAAM,CAAC,QAAQ,CAAA,GAAI,UAAU,KAAA,CAAM,CAAA,CAAE;YAErC,MAAM,6BAA6B,SAAS,aAAA;YAC5C,KAAA,MAAW,aAAa,WAAY;gBAElC,IAAI,cAAc,2BAA4B,CAAA;gBAC9C,sBAAA,gCAAA,UAAW,cAAA,CAAe;oBAAE,OAAO;gBAAU,CAAC;gBAE9C,IAAI,cAAc,aAAa,SAAU,CAAA,SAAS,SAAA,GAAY;gBAC9D,IAAI,cAAc,YAAY,SAAU,CAAA,SAAS,SAAA,GAAY,SAAS,YAAA;gBACtE,sBAAA,gCAAA,UAAW,KAAA,CAAM;gBACjB,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;YAC7D;QACF;oDACA;QAAC;QAAU,QAAQ;KAAA;IAGrB,MAAM,oBAA0B,4KAAA;4DAC9B,IAAM,WAAW;gBAAC;gBAAc,OAAO;aAAC;2DACxC;QAAC;QAAY;QAAc,OAAO;KAAA;IAK9B,0KAAA;uCAAU,MAAM;YACpB,IAAI,cAAc;gBAChB,kBAAkB;YACpB;QACF;sCAAG;QAAC;QAAc,iBAAiB;KAAC;IAIpC,MAAM,EAAE,YAAA,EAAc,wBAAA,CAAyB,CAAA,GAAI;IAC7C,0KAAA;uCAAU,MAAM;YACpB,IAAI,SAAS;gBACX,IAAI,mBAAmB;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBAEpC,MAAM;qEAAoB,CAAC,UAAwB;4BAER;iEACA;wBAFzC,mBAAmB;4BACjB,GAAG,KAAK,GAAA,CAAI,KAAK,KAAA,CAAM,MAAM,KAAK,IAAA,sGAA8B,OAAA,wGAAS,CAAA,qGAAK,CAAA,CAAE;4BAChF,GAAG,KAAK,GAAA,CAAI,KAAK,KAAA,CAAM,MAAM,KAAK,IAAA,uGAA8B,OAAA,0GAAS,CAAA,qGAAK,CAAA,CAAE;wBAClF;oBACF;;gBACA,MAAM;mEAAkB,CAAC,UAAwB;wBAE/C,IAAI,iBAAiB,CAAA,IAAK,MAAM,iBAAiB,CAAA,IAAK,IAAI;4BACxD,MAAM,cAAA,CAAe;wBACvB,OAAO;4BAEL,IAAI,CAAC,QAAQ,QAAA,CAAS,MAAM,MAAqB,GAAG;gCAClD,aAAa,KAAK;4BACpB;wBACF;wBACA,SAAS,mBAAA,CAAoB,eAAe,iBAAiB;wBAC7D,yBAAyB,OAAA,GAAU;oBACrC;;gBAEA,IAAI,yBAAyB,OAAA,KAAY,MAAM;oBAC7C,SAAS,gBAAA,CAAiB,eAAe,iBAAiB;oBAC1D,SAAS,gBAAA,CAAiB,aAAa,iBAAiB;wBAAE,SAAS;wBAAM,MAAM;oBAAK,CAAC;gBACvF;gBAEA;mDAAO,MAAM;wBACX,SAAS,mBAAA,CAAoB,eAAe,iBAAiB;wBAC7D,SAAS,mBAAA,CAAoB,aAAa,iBAAiB;4BAAE,SAAS;wBAAK,CAAC;oBAC9E;;YACF;QACF;sCAAG;QAAC;QAAS;QAAc,wBAAwB;KAAC;IAE9C,0KAAA;uCAAU,MAAM;YACpB,MAAM;qDAAQ,IAAM,aAAa,KAAK;;YACtC,OAAO,gBAAA,CAAiB,QAAQ,KAAK;YACrC,OAAO,gBAAA,CAAiB,UAAU,KAAK;YACvC;+CAAO,MAAM;oBACX,OAAO,mBAAA,CAAoB,QAAQ,KAAK;oBACxC,OAAO,mBAAA,CAAoB,UAAU,KAAK;gBAC5C;;QACF;sCAAG;QAAC,YAAY;KAAC;IAEjB,MAAM,CAAC,WAAW,qBAAqB,CAAA,GAAI;gDAAmB,CAAC,WAAW;YACxE,MAAM,eAAe,SAAS,EAAE,MAAA;qEAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;;YAC/D,MAAM,cAAc,aAAa,IAAA;oEAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,SAAS,aAAa;;YAC3F,MAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;YAC/D,IAAI,UAAU;gBAKZ;4DAAW,IAAO,SAAS,GAAA,CAAI,OAAA,CAAwB,KAAA,CAAM,CAAC;;YAChE;QACF,CAAC;;IAED,MAAM,kBAAwB,4KAAA;0DAC5B,CAAC,MAAgC,OAAe,aAAsB;YACpE,MAAM,mBAAmB,CAAC,uBAAuB,OAAA,IAAW,CAAC;YAC7D,MAAM,iBAAiB,QAAQ,KAAA,KAAU,KAAA,KAAa,QAAQ,KAAA,KAAU;YACxE,IAAI,kBAAkB,kBAAkB;gBACtC,gBAAgB,IAAI;gBACpB,IAAI,iBAAkB,CAAA,uBAAuB,OAAA,GAAU;YACzD;QACF;yDACA;QAAC,QAAQ,KAAK;KAAA;IAEhB,MAAM,kBAAwB,4KAAA;0DAAY,sDAAM,QAAS,KAAA,CAAM;yDAAG;QAAC,OAAO;KAAC;IAC3E,MAAM,sBAA4B,4KAAA;8DAChC,CAAC,MAAoC,OAAe,aAAsB;YACxE,MAAM,mBAAmB,CAAC,uBAAuB,OAAA,IAAW,CAAC;YAC7D,MAAM,iBAAiB,QAAQ,KAAA,KAAU,KAAA,KAAa,QAAQ,KAAA,KAAU;YACxE,IAAI,kBAAkB,kBAAkB;gBACtC,oBAAoB,IAAI;YAC1B;QACF;6DACA;QAAC,QAAQ,KAAK;KAAA;IAGhB,MAAM,iBAAiB,aAAa,WAAW,uBAAuB;IAGtE,MAAM,qBACJ,mBAAmB,uBACf;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF,IACA,CAAC;IAEP,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA;QACA,kBAAkB;QAClB;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QAEA,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,wOAAA,EAAA;YAAa,IAAI;YAAM,gBAAc;YACpC,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,2LAAA,EAAA;gBACC,SAAO;gBAGP,SAAS,QAAQ,IAAA;gBACjB,kBAAkB,CAAC,UAAU;oBAE3B,MAAM,cAAA,CAAe;gBACvB;gBACA,wBAAoB,uLAAA,EAAqB,kBAAkB,CAAC,UAAU;wBACpE;qBAAA,mBAAA,QAAQ,OAAA,cAAR,uCAAA,iBAAiB,KAAA,CAAM;wBAAE,eAAe;oBAAK,CAAC;oBAC9C,MAAM,cAAA,CAAe;gBACvB,CAAC;gBAED,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,uMAAA,EAAA;oBACC,SAAO;oBACP,6BAA2B;oBAC3B;oBACA;oBAGA,gBAAgB,CAAC,QAAU,MAAM,cAAA,CAAe;oBAChD,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;oBAE3C,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,gBAAA;wBACC,MAAK;wBACL,IAAI,QAAQ,SAAA;wBACZ,cAAY,QAAQ,IAAA,GAAO,SAAS;wBACpC,KAAK,QAAQ,GAAA;wBACb,eAAe,CAAC,QAAU,MAAM,cAAA,CAAe;wBAC9C,GAAG,YAAA;wBACH,GAAG,kBAAA;wBACJ,UAAU,IAAM,gBAAgB,IAAI;wBACpC,KAAK;wBACL,OAAO;4BAAA,0DAAA;4BAEL,SAAS;4BACT,eAAe;4BAAA,8DAAA;4BAEf,SAAS;4BACT,GAAG,aAAa,KAAA;wBAClB;wBACA,WAAW,2LAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;4BACjE,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;4BAG7D,IAAI,MAAM,GAAA,KAAQ,MAAO,CAAA,MAAM,cAAA,CAAe;4BAE9C,IAAI,CAAC,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW,EAAG,CAAA,sBAAsB,MAAM,GAAG;4BAE7E,IAAI;gCAAC;gCAAW;gCAAa;gCAAQ,KAAK;6BAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;gCAC/D,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;gCACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;gCAE1D,IAAI;oCAAC;oCAAW,KAAK;iCAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oCAC1C,iBAAiB,eAAe,KAAA,CAAM,EAAE,OAAA,CAAQ;gCAClD;gCACA,IAAI;oCAAC;oCAAW,WAAW;iCAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oCAChD,MAAM,iBAAiB,MAAM,MAAA;oCAC7B,MAAM,eAAe,eAAe,OAAA,CAAQ,cAAc;oCAC1D,iBAAiB,eAAe,KAAA,CAAM,eAAe,CAAC;gCACxD;gCAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gCAE3C,MAAM,cAAA,CAAe;4BACvB;wBACF,CAAC;oBAAA;gBACH;YACF;QACF,CACF;IAAA;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,6BAA6B;AAKnC,IAAM,4BAAkC,2KAAA,CAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,aAAA,EAAe,QAAA,EAAU,GAAG,YAAY,CAAA,GAAI;IACpD,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,iBAAiB,wBAAwB,cAAc,aAAa;IAC1E,MAAM,CAAC,gBAAgB,iBAAiB,CAAA,GAAU,yKAAA,CAAgC,IAAI;IACtF,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,yKAAA,CAAkD,IAAI;IAC1F,MAAM,mBAAe,iMAAA,EAAgB;mEAAc,CAAC,OAAS,WAAW,IAAI,CAAC;;IAC7E,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,0BAAgC,uKAAA,CAAO,KAAK;IAClD,MAAM,sBAA4B,uKAAA,CAAO,IAAI;IAE7C,MAAM,EAAE,QAAA,EAAU,YAAA,EAAc,gBAAA,EAAkB,iBAAA,CAAkB,CAAA,GAAI;IACxE,MAAM,WAAiB,4KAAA;2DAAY,MAAM;YACvC,IACE,QAAQ,OAAA,IACR,QAAQ,SAAA,IACR,kBACA,WACA,YACA,gBACA,kBACA;gBACA,MAAM,cAAc,QAAQ,OAAA,CAAQ,qBAAA,CAAsB;gBAK1D,MAAM,cAAc,QAAQ,qBAAA,CAAsB;gBAClD,MAAM,gBAAgB,QAAQ,SAAA,CAAU,qBAAA,CAAsB;gBAC9D,MAAM,eAAe,iBAAiB,qBAAA,CAAsB;gBAE5D,IAAI,QAAQ,GAAA,KAAQ,OAAO;oBACzB,MAAM,iBAAiB,aAAa,IAAA,GAAO,YAAY,IAAA;oBACvD,MAAM,OAAO,cAAc,IAAA,GAAO;oBAClC,MAAM,YAAY,YAAY,IAAA,GAAO;oBACrC,MAAM,kBAAkB,YAAY,KAAA,GAAQ;oBAC5C,MAAM,eAAe,KAAK,GAAA,CAAI,iBAAiB,YAAY,KAAK;oBAChE,MAAM,YAAY,OAAO,UAAA,GAAa;oBACtC,MAAM,kBAAc,qKAAA,EAAM,MAAM;wBAC9B;wBAAA,+DAAA;wBAAA,iEAAA;wBAAA,qEAAA;wBAAA,gBAAA;wBAAA,qDAAA;wBAMA,KAAK,GAAA,CAAI,gBAAgB,YAAY,YAAY;qBAClD;oBAED,eAAe,KAAA,CAAM,QAAA,GAAW,kBAAkB;oBAClD,eAAe,KAAA,CAAM,IAAA,GAAO,cAAc;gBAC5C,OAAO;oBACL,MAAM,iBAAiB,YAAY,KAAA,GAAQ,aAAa,KAAA;oBACxD,MAAM,QAAQ,OAAO,UAAA,GAAa,cAAc,KAAA,GAAQ;oBACxD,MAAM,aAAa,OAAO,UAAA,GAAa,YAAY,KAAA,GAAQ;oBAC3D,MAAM,kBAAkB,YAAY,KAAA,GAAQ;oBAC5C,MAAM,eAAe,KAAK,GAAA,CAAI,iBAAiB,YAAY,KAAK;oBAChE,MAAM,WAAW,OAAO,UAAA,GAAa;oBACrC,MAAM,mBAAe,qKAAA,EAAM,OAAO;wBAChC;wBACA,KAAK,GAAA,CAAI,gBAAgB,WAAW,YAAY;qBACjD;oBAED,eAAe,KAAA,CAAM,QAAA,GAAW,kBAAkB;oBAClD,eAAe,KAAA,CAAM,KAAA,GAAQ,eAAe;gBAC9C;gBAKA,MAAM,QAAQ,SAAS;gBACvB,MAAM,kBAAkB,OAAO,WAAA,GAAc,iBAAiB;gBAC9D,MAAM,cAAc,SAAS,YAAA;gBAE7B,MAAM,gBAAgB,OAAO,gBAAA,CAAiB,OAAO;gBACrD,MAAM,wBAAwB,SAAS,cAAc,cAAA,EAAgB,EAAE;gBACvE,MAAM,oBAAoB,SAAS,cAAc,UAAA,EAAY,EAAE;gBAC/D,MAAM,2BAA2B,SAAS,cAAc,iBAAA,EAAmB,EAAE;gBAC7E,MAAM,uBAAuB,SAAS,cAAc,aAAA,EAAe,EAAE;gBACrE,MAAM,oBAAoB,wBAAwB,oBAAoB,cAAc,uBAAuB;gBAC3G,MAAM,mBAAmB,KAAK,GAAA,CAAI,aAAa,YAAA,GAAe,GAAG,iBAAiB;gBAElF,MAAM,iBAAiB,OAAO,gBAAA,CAAiB,QAAQ;gBACvD,MAAM,qBAAqB,SAAS,eAAe,UAAA,EAAY,EAAE;gBACjE,MAAM,wBAAwB,SAAS,eAAe,aAAA,EAAe,EAAE;gBAEvE,MAAM,yBAAyB,YAAY,GAAA,GAAM,YAAY,MAAA,GAAS,IAAI;gBAC1E,MAAM,4BAA4B,kBAAkB;gBAEpD,MAAM,yBAAyB,aAAa,YAAA,GAAe;gBAC3D,MAAM,mBAAmB,aAAa,SAAA,GAAY;gBAClD,MAAM,yBAAyB,wBAAwB,oBAAoB;gBAC3E,MAAM,4BAA4B,oBAAoB;gBAEtD,MAAM,8BAA8B,0BAA0B;gBAE9D,IAAI,6BAA6B;oBAC/B,MAAM,aACJ,MAAM,MAAA,GAAS,KAAK,iBAAiB,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,CAAG,GAAA,CAAI,OAAA;oBACpE,eAAe,KAAA,CAAM,MAAA,GAAS;oBAC9B,MAAM,uBACJ,QAAQ,YAAA,GAAe,SAAS,SAAA,GAAY,SAAS,YAAA;oBACvD,MAAM,mCAAmC,KAAK,GAAA,CAC5C,2BACA,yBAAA,gFAAA;oBAAA,CAEG,aAAa,wBAAwB,CAAA,IACtC,uBACA;oBAEJ,MAAM,SAAS,yBAAyB;oBACxC,eAAe,KAAA,CAAM,MAAA,GAAS,SAAS;gBACzC,OAAO;oBACL,MAAM,cAAc,MAAM,MAAA,GAAS,KAAK,iBAAiB,KAAA,CAAM,CAAC,CAAA,CAAG,GAAA,CAAI,OAAA;oBACvE,eAAe,KAAA,CAAM,GAAA,GAAM;oBAC3B,MAAM,gCAAgC,KAAK,GAAA,CACzC,wBACA,wBACE,SAAS,SAAA,GAAA,6EAAA;oBAAA,CAER,cAAc,qBAAqB,CAAA,IACpC;oBAEJ,MAAM,SAAS,gCAAgC;oBAC/C,eAAe,KAAA,CAAM,MAAA,GAAS,SAAS;oBACvC,SAAS,SAAA,GAAY,yBAAyB,yBAAyB,SAAS,SAAA;gBAClF;gBAEA,eAAe,KAAA,CAAM,MAAA,GAAS,GAAiB,OAAd,cAAc,EAAA;gBAC/C,eAAe,KAAA,CAAM,SAAA,GAAY,mBAAmB;gBACpD,eAAe,KAAA,CAAM,SAAA,GAAY,kBAAkB;gBAGnD,qBAAA,+BAAA,WAAW;gBAIX;uEAAsB,IAAO,wBAAwB,OAAA,GAAU,IAAK;;YACtE;QACF;0DAAG;QACD;QACA,QAAQ,OAAA;QACR,QAAQ,SAAA;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ,GAAA;QACR;KACD;IAED,IAAA,yMAAA;qDAAgB,IAAM,SAAS;oDAAG;QAAC,QAAQ;KAAC;IAG5C,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,yKAAA,CAAiB;IACjE,IAAA,yMAAA;qDAAgB,MAAM;YACpB,IAAI,QAAS,CAAA,iBAAiB,OAAO,gBAAA,CAAiB,OAAO,EAAE,MAAM;QACvE;oDAAG;QAAC,OAAO;KAAC;IAMZ,MAAM,2BAAiC,4KAAA;2EACrC,CAAC,SAA+C;YAC9C,IAAI,QAAQ,oBAAoB,OAAA,KAAY,MAAM;gBAChD,SAAS;gBACT,8BAAA,wCAAA,oBAAoB;gBACpB,oBAAoB,OAAA,GAAU;YAChC;QACF;0EACA;QAAC;QAAU,iBAAiB;KAAA;IAG9B,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,wBAAA;QACC,OAAO;QACP;QACA;QACA,sBAAsB;QAEtB,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,OAAA;YACC,KAAK;YACL,OAAO;gBACL,SAAS;gBACT,eAAe;gBACf,UAAU;gBACV,QAAQ;YACV;YAEA,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;gBACE,GAAG,WAAA;gBACJ,KAAK;gBACL,OAAO;oBAAA,gFAAA;oBAAA,2EAAA;oBAGL,WAAW;oBAAA,oEAAA;oBAEX,WAAW;oBACX,GAAG,YAAY,KAAA;gBACjB;YAAA;QACF;IACF;AAGN,CAAC;AAED,0BAA0B,WAAA,GAAc;AAMxC,IAAM,uBAAuB;AAM7B,IAAM,uBAA6B,2KAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,aAAA,EACA,QAAQ,OAAA,EACR,mBAAmB,cAAA,EACnB,GAAG,aACL,GAAI;IACJ,MAAM,cAAc,eAAe,aAAa;IAEhD,OACE,aAAA,GAAA,IAAA,6KAAA,EAAiB,gLAAA,EAAhB;QACE,GAAG,WAAA;QACH,GAAG,WAAA;QACJ,KAAK;QACL;QACA;QACA,OAAO;YAAA,iDAAA;YAEL,WAAW;YACX,GAAG,YAAY,KAAA;YAAA,iDAAA;YAEf,GAAG;gBACD,2CAA2C;gBAC3C,0CAA0C;gBAC1C,2CAA2C;gBAC3C,gCAAgC;gBAChC,iCAAiC;YACnC,CAAA;QACF;IAAA;AAGN,CAAC;AAED,qBAAqB,WAAA,GAAc;AAYnC,IAAM,CAAC,wBAAwB,wBAAwB,CAAA,GACrD,oBAAgD,cAAc,CAAC,CAAC;AAElE,IAAM,gBAAgB;AAQtB,IAAM,iBAAuB,2KAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IACnD,MAAM,iBAAiB,wBAAwB,eAAe,aAAa;IAC3E,MAAM,kBAAkB,yBAAyB,eAAe,aAAa;IAC7E,MAAM,mBAAe,iMAAA,EAAgB,cAAc,eAAe,gBAAgB;IAClF,MAAM,mBAAyB,uKAAA,CAAO,CAAC;IACvC,OACE,aAAA,GAAA,IAAA,8KAAA,EAAA,kLAAA,EAAA;QAEE,UAAA;YAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,SAAA;gBACC,yBAAyB;oBACvB,QAAQ;gBACV;gBACA;YAAA;YAEF,aAAA,GAAA,IAAA,6KAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO;gBACtB,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;oBACC,8BAA2B;oBAC3B,MAAK;oBACJ,GAAG,aAAA;oBACJ,KAAK;oBACL,OAAO;wBAAA,0EAAA;wBAAA,mFAAA;wBAAA,uCAAA;wBAIL,UAAU;wBACV,MAAM;wBAAA,gEAAA;wBAAA,8DAAA;wBAAA,yCAAA;wBAAA,2DAAA;wBAKN,UAAU;wBACV,GAAG,cAAc,KAAA;oBACnB;oBACA,cAAU,uLAAA,EAAqB,cAAc,QAAA,EAAU,CAAC,UAAU;wBAChE,MAAM,WAAW,MAAM,aAAA;wBACvB,MAAM,EAAE,cAAA,EAAgB,uBAAA,CAAwB,CAAA,GAAI;wBACpD,uFAAI,wBAAyB,OAAA,KAAW,gBAAgB;4BACtD,MAAM,aAAa,KAAK,GAAA,CAAI,iBAAiB,OAAA,GAAU,SAAS,SAAS;4BACzE,IAAI,aAAa,GAAG;gCAClB,MAAM,kBAAkB,OAAO,WAAA,GAAc,iBAAiB;gCAC9D,MAAM,eAAe,WAAW,eAAe,KAAA,CAAM,SAAS;gCAC9D,MAAM,YAAY,WAAW,eAAe,KAAA,CAAM,MAAM;gCACxD,MAAM,aAAa,KAAK,GAAA,CAAI,cAAc,SAAS;gCAEnD,IAAI,aAAa,iBAAiB;oCAChC,MAAM,aAAa,aAAa;oCAChC,MAAM,oBAAoB,KAAK,GAAA,CAAI,iBAAiB,UAAU;oCAC9D,MAAM,aAAa,aAAa;oCAEhC,eAAe,KAAA,CAAM,MAAA,GAAS,oBAAoB;oCAClD,IAAI,eAAe,KAAA,CAAM,MAAA,KAAW,OAAO;wCACzC,SAAS,SAAA,GAAY,aAAa,IAAI,aAAa;wCAEnD,eAAe,KAAA,CAAM,cAAA,GAAiB;oCACxC;gCACF;4BACF;wBACF;wBACA,iBAAiB,OAAA,GAAU,SAAS,SAAA;oBACtC,CAAC;gBAAA;YACH,CACF;SAAA;IAAA,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,aAAa;AAInB,IAAM,CAAC,4BAA4B,qBAAqB,CAAA,GACtD,oBAA6C,UAAU;AAKzD,IAAM,cAAoB,2KAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,cAAU,0KAAA,CAAM;IACtB,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,4BAAA;QAA2B,OAAO;QAAe,IAAI;QACpD,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;YAAc,MAAK;YAAQ,mBAAiB;YAAU,GAAG,UAAA;YAAY,KAAK;QAAA,CAAc;IAAA,CAC3F;AAEJ;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB,2KAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,eAAe,sBAAsB,YAAY,aAAa;IACpE,OAAO,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;QAAc,IAAI,aAAa,EAAA;QAAK,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,YAAY;AAUlB,IAAM,CAAC,2BAA2B,oBAAoB,CAAA,GACpD,oBAA4C,SAAS;AASvD,IAAM,aAAmB,2KAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EACJ,aAAA,EACA,KAAA,EACA,WAAW,KAAA,EACX,WAAW,aAAA,EACX,GAAG,WACL,GAAI;IACJ,MAAM,UAAU,iBAAiB,WAAW,aAAa;IACzD,MAAM,iBAAiB,wBAAwB,WAAW,aAAa;IACvE,MAAM,aAAa,QAAQ,KAAA,KAAU;IACrC,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,yKAAA,sDAAS,gBAAiB,EAAE;IACpE,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,yKAAA,CAAS,KAAK;IACtD,MAAM,mBAAe,iMAAA,EAAgB;oDAAc,CAAC;;qEACnC,eAAA,yGAAf,gBAAiC,MAAM,OAAO,QAAQ;;;IAExD,MAAM,aAAS,0KAAA,CAAM;IACrB,MAAM,iBAAuB,uKAAA,CAA0C,OAAO;IAE9E,MAAM,eAAe,MAAM;QACzB,IAAI,CAAC,UAAU;YACb,QAAQ,aAAA,CAAc,KAAK;YAC3B,QAAQ,YAAA,CAAa,KAAK;QAC5B;IACF;IAEA,IAAI,UAAU,IAAI;QAChB,MAAM,IAAI,MACR;IAEJ;IAEA,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,2BAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA,kBAAwB,4KAAA;sCAAY,CAAC,SAAS;gBAC5C;8CAAa,CAAC;;+BAAkB,iBAAA,uCAAkB,gCAAM,WAAA,iEAAe,EAAA,EAAI,IAAA,CAAK,CAAC;;;YACnF;qCAAG,CAAC,CAAC;QAEL,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,WAAW,QAAA,EAAX;YACC,OAAO;YACP;YACA;YACA;YAEA,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;gBACC,MAAK;gBACL,mBAAiB;gBACjB,oBAAkB,YAAY,KAAK,KAAA;gBAEnC,iBAAe,cAAc;gBAC7B,cAAY,aAAa,YAAY;gBACrC,iBAAe,YAAY,KAAA;gBAC3B,iBAAe,WAAW,KAAK,KAAA;gBAC/B,UAAU,WAAW,KAAA,IAAY,CAAA;gBAChC,GAAG,SAAA;gBACJ,KAAK;gBACL,aAAS,uLAAA,EAAqB,UAAU,OAAA,EAAS,IAAM,aAAa,IAAI,CAAC;gBACzE,YAAQ,uLAAA,EAAqB,UAAU,MAAA,EAAQ,IAAM,aAAa,KAAK,CAAC;gBACxE,aAAS,uLAAA,EAAqB,UAAU,OAAA,EAAS,MAAM;oBAErD,IAAI,eAAe,OAAA,KAAY,QAAS,CAAA,aAAa;gBACvD,CAAC;gBACD,iBAAa,uLAAA,EAAqB,UAAU,WAAA,EAAa,MAAM;oBAG7D,IAAI,eAAe,OAAA,KAAY,QAAS,CAAA,aAAa;gBACvD,CAAC;gBACD,mBAAe,uLAAA,EAAqB,UAAU,aAAA,EAAe,CAAC,UAAU;oBACtE,eAAe,OAAA,GAAU,MAAM,WAAA;gBACjC,CAAC;gBACD,mBAAe,uLAAA,EAAqB,UAAU,aAAA,EAAe,CAAC,UAAU;oBAEtE,eAAe,OAAA,GAAU,MAAM,WAAA;oBAC/B,IAAI,UAAU;4BACZ;yBAAA,8BAAA,eAAe,WAAA,GAAc,WAA7B,kDAAA,iCAAA;oBACF,OAAA,IAAW,eAAe,OAAA,KAAY,SAAS;wBAG7C,MAAM,aAAA,CAAc,KAAA,CAAM;4BAAE,eAAe;wBAAK,CAAC;oBACnD;gBACF,CAAC;gBACD,oBAAgB,uLAAA,EAAqB,UAAU,cAAA,EAAgB,CAAC,UAAU;oBACxE,IAAI,MAAM,aAAA,KAAkB,SAAS,aAAA,EAAe;4BAClD;yBAAA,8BAAA,eAAe,WAAA,GAAc,WAA7B,kDAAA,iCAAA;oBACF;gBACF,CAAC;gBACD,eAAW,uLAAA,EAAqB,UAAU,SAAA,EAAW,CAAC,UAAU;wBACxC;oBAAtB,MAAM,6DAA+B,SAAA,wFAAW,OAAA,MAAY;oBAC5D,IAAI,iBAAiB,MAAM,GAAA,KAAQ,IAAK,CAAA;oBACxC,IAAI,eAAe,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,aAAa;oBAErD,IAAI,MAAM,GAAA,KAAQ,IAAK,CAAA,MAAM,cAAA,CAAe;gBAC9C,CAAC;YAAA;QACH;IACF;AAGN;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,iBAAiB;AAKvB,IAAM,iBAAuB,2KAAA,CAC3B,CAAC,OAAyC,iBAAiB;IAEzD,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IAC9D,MAAM,UAAU,iBAAiB,gBAAgB,aAAa;IAC9D,MAAM,iBAAiB,wBAAwB,gBAAgB,aAAa;IAC5E,MAAM,cAAc,qBAAqB,gBAAgB,aAAa;IACtE,MAAM,uBAAuB,8BAA8B,gBAAgB,aAAa;IACxF,MAAM,CAAC,cAAc,eAAe,CAAA,GAAU,yKAAA,CAAuC,IAAI;IACzF,MAAM,mBAAe,iMAAA,EACnB;wDACA,CAAC,OAAS,gBAAgB,IAAI;uDAC9B,YAAY,gBAAA;wDACZ,CAAC;;yEAAwB,mBAAA,iHAAf,gBAAqC,MAAM,YAAY,KAAA,EAAO,YAAY,QAAQ;;;IAG9F,MAAM,0EAAc,aAAc,WAAA;IAClC,MAAM,eAAqB,wKAAA;gDACzB,IACE,aAAA,GAAA,IAAA,6KAAA,EAAC,UAAA;gBAA+B,OAAO,YAAY,KAAA;gBAAO,UAAU,YAAY,QAAA;gBAC7E,UAAA;YAAA,GADU,YAAY,KAEzB;+CAEF;QAAC,YAAY,QAAA;QAAU,YAAY,KAAA;QAAO,WAAW;KAAA;IAGvD,MAAM,EAAE,iBAAA,EAAmB,oBAAA,CAAqB,CAAA,GAAI;IACpD,IAAA,yMAAA;0CAAgB,MAAM;YACpB,kBAAkB,YAAY;YAC9B;kDAAO,IAAM,qBAAqB,YAAY;;QAChD;yCAAG;QAAC;QAAmB;QAAsB,YAAY;KAAC;IAE1D,OACE,aAAA,GAAA,IAAA,8KAAA,EAAA,kLAAA,EAAA;QACE,UAAA;YAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,IAAA,EAAV;gBAAe,IAAI,YAAY,MAAA;gBAAS,GAAG,aAAA;gBAAe,KAAK;YAAA,CAAc;YAG7E,YAAY,UAAA,IAAc,QAAQ,SAAA,IAAa,CAAC,QAAQ,oBAAA,GAC5C,oLAAA,CAAa,cAAc,QAAA,EAAU,QAAQ,SAAS,IAC/D;SAAA;IAAA,CACN;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,sBAAsB;AAK5B,IAAM,sBAA4B,2KAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,aAAA,EAAe,GAAG,mBAAmB,CAAA,GAAI;IACjD,MAAM,cAAc,qBAAqB,qBAAqB,aAAa;IAC3E,OAAO,YAAY,UAAA,GACjB,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,IAAA,EAAV;QAAe,eAAW;QAAE,GAAG,kBAAA;QAAoB,KAAK;IAAA,CAAc,IACrE;AACN;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,wBAAwB;AAK9B,IAAM,uBAA6B,2KAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,iBAAiB,wBAAwB,uBAAuB,MAAM,aAAa;IACzF,MAAM,kBAAkB,yBAAyB,uBAAuB,MAAM,aAAa;IAC3F,MAAM,CAAC,aAAa,cAAc,CAAA,GAAU,yKAAA,CAAS,KAAK;IAC1D,MAAM,eAAe,qMAAA,EAAgB,cAAc,gBAAgB,oBAAoB;IAEvF,IAAA,yMAAA;gDAAgB,MAAM;YACpB,IAAI,eAAe,QAAA,IAAY,eAAe,YAAA,EAAc;gBAE1D,IAASA;0EAAT,WAAwB;wBACtB,MAAMC,eAAc,SAAS,SAAA,GAAY;wBACzC,eAAeA,YAAW;oBAC5B;;gBAHS,IAAA,eAAAD;gBADT,MAAM,WAAW,eAAe,QAAA;gBAKhCA,cAAa;gBACb,SAAS,gBAAA,CAAiB,UAAUA,aAAY;gBAChD;4DAAO,IAAM,SAAS,mBAAA,CAAoB,UAAUA,aAAY;;YAClE;QACF;+CAAG;QAAC,eAAe,QAAA;QAAU,eAAe,YAAY;KAAC;IAEzD,OAAO,cACL,aAAA,GAAA,IAAA,6KAAA,EAAC,wBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,cAAc,MAAM;YAClB,MAAM,EAAE,QAAA,EAAU,YAAA,CAAa,CAAA,GAAI;YACnC,IAAI,YAAY,cAAc;gBAC5B,SAAS,SAAA,GAAY,SAAS,SAAA,GAAY,aAAa,YAAA;YACzD;QACF;IAAA,KAEA;AACN,CAAC;AAED,qBAAqB,WAAA,GAAc;AAMnC,IAAM,0BAA0B;AAKhC,IAAM,yBAA+B,2KAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,iBAAiB,wBAAwB,yBAAyB,MAAM,aAAa;IAC3F,MAAM,kBAAkB,yBAAyB,yBAAyB,MAAM,aAAa;IAC7F,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,yKAAA,CAAS,KAAK;IAC9D,MAAM,mBAAe,iMAAA,EAAgB,cAAc,gBAAgB,oBAAoB;IAEvF,IAAA,yMAAA;kDAAgB,MAAM;YACpB,IAAI,eAAe,QAAA,IAAY,eAAe,YAAA,EAAc;gBAE1D,IAASA;4EAAT,WAAwB;wBACtB,MAAM,YAAY,SAAS,YAAA,GAAe,SAAS,YAAA;wBAGnD,MAAME,iBAAgB,KAAK,IAAA,CAAK,SAAS,SAAS,IAAI;wBACtD,iBAAiBA,cAAa;oBAChC;;gBANS,IAAA,eAAAF;gBADT,MAAM,WAAW,eAAe,QAAA;gBAQhCA,cAAa;gBACb,SAAS,gBAAA,CAAiB,UAAUA,aAAY;gBAChD;8DAAO,IAAM,SAAS,mBAAA,CAAoB,UAAUA,aAAY;;YAClE;QACF;iDAAG;QAAC,eAAe,QAAA;QAAU,eAAe,YAAY;KAAC;IAEzD,OAAO,gBACL,aAAA,GAAA,IAAA,6KAAA,EAAC,wBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,cAAc,MAAM;YAClB,MAAM,EAAE,QAAA,EAAU,YAAA,CAAa,CAAA,GAAI;YACnC,IAAI,YAAY,cAAc;gBAC5B,SAAS,SAAA,GAAY,SAAS,SAAA,GAAY,aAAa,YAAA;YACzD;QACF;IAAA,KAEA;AACN,CAAC;AAED,uBAAuB,WAAA,GAAc;AAOrC,IAAM,yBAA+B,2KAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,aAAA,EAAe,YAAA,EAAc,GAAG,qBAAqB,CAAA,GAAI;IACjE,MAAM,iBAAiB,wBAAwB,sBAAsB,aAAa;IAClF,MAAM,qBAA2B,uKAAA,CAAsB,IAAI;IAC3D,MAAM,WAAW,cAAc,aAAa;IAE5C,MAAM,uBAA6B,4KAAA;oEAAY,MAAM;YACnD,IAAI,mBAAmB,OAAA,KAAY,MAAM;gBACvC,OAAO,aAAA,CAAc,mBAAmB,OAAO;gBAC/C,mBAAmB,OAAA,GAAU;YAC/B;QACF;mEAAG,CAAC,CAAC;IAEC,0KAAA;4CAAU,MAAM;YACpB;oDAAO,IAAM,qBAAqB;;QACpC;2CAAG;QAAC,oBAAoB;KAAC;IAMzB,IAAA,yMAAA;kDAAgB,MAAM;gBAEpB;YADA,MAAM,aAAa,SAAS,EAAE,IAAA;qEAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,SAAS,aAAa;;YACxF,uBAAA,kCAAA,0BAAA,WAAY,GAAA,CAAI,OAAA,cAAhB,8CAAA,wBAAyB,cAAA,CAAe;gBAAE,OAAO;YAAU,CAAC;QAC9D;iDAAG;QAAC,QAAQ;KAAC;IAEb,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;QACC,eAAW;QACV,GAAG,oBAAA;QACJ,KAAK;QACL,OAAO;YAAE,YAAY;YAAG,GAAG,qBAAqB,KAAA;QAAM;QACtD,mBAAe,uLAAA,EAAqB,qBAAqB,aAAA,EAAe,MAAM;YAC5E,IAAI,mBAAmB,OAAA,KAAY,MAAM;gBACvC,mBAAmB,OAAA,GAAU,OAAO,WAAA,CAAY,cAAc,EAAE;YAClE;QACF,CAAC;QACD,mBAAe,uLAAA,EAAqB,qBAAqB,aAAA,EAAe,MAAM;gBAC5E;aAAA,8BAAA,eAAe,WAAA,GAAc,WAA7B,kDAAA,iCAAA;YACA,IAAI,mBAAmB,OAAA,KAAY,MAAM;gBACvC,mBAAmB,OAAA,GAAU,OAAO,WAAA,CAAY,cAAc,EAAE;YAClE;QACF,CAAC;QACD,gBAAgB,2LAAA,EAAqB,qBAAqB,cAAA,EAAgB,MAAM;YAC9E,qBAAqB;QACvB,CAAC;IAAA;AAGP,CAAC;AAMD,IAAM,iBAAiB;AAKvB,IAAM,kBAAwB,2KAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,aAAA,EAAe,GAAG,eAAe,CAAA,GAAI;IAC7C,OAAO,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;QAAc,eAAW;QAAE,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AAC3E;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,aAAa;AAMnB,IAAM,cAAoB,2KAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,iBAAiB,wBAAwB,YAAY,aAAa;IACxE,OAAO,QAAQ,IAAA,IAAQ,eAAe,QAAA,KAAa,WACjD,aAAA,GAAA,IAAA,6KAAA,EAAiB,8KAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc,IACzE;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,oBAAoB;AAK1B,IAAM,oBAA0B,2KAAA,CAC9B,QAA0E,iBAAiB;QAA1F,EAAE,aAAA,EAAe,KAAA,EAAO,GAAG,MAAM,CAAA;IAChC,MAAM,MAAY,uKAAA,CAA0B,IAAI;IAChD,MAAM,mBAAe,iMAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,gBAAY,6LAAA,EAAY,KAAK;IAG7B,0KAAA;uCAAU,MAAM;YACpB,MAAM,SAAS,IAAI,OAAA;YACnB,IAAI,CAAC,OAAQ,CAAA;YAEb,MAAM,cAAc,OAAO,iBAAA,CAAkB,SAAA;YAC7C,MAAM,aAAa,OAAO,wBAAA,CACxB,aACA;YAEF,MAAM,WAAW,WAAW,GAAA;YAC5B,IAAI,cAAc,SAAS,UAAU;gBACnC,MAAM,QAAQ,IAAI,MAAM,UAAU;oBAAE,SAAS;gBAAK,CAAC;gBACnD,SAAS,IAAA,CAAK,QAAQ,KAAK;gBAC3B,OAAO,aAAA,CAAc,KAAK;YAC5B;QACF;sCAAG;QAAC;QAAW,KAAK;KAAC;IAcrB,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,MAAA,EAAV;QACE,GAAG,KAAA;QACJ,OAAO;YAAE,GAAG,2MAAA;YAAwB,GAAG,MAAM,KAAA;QAAM;QACnD,KAAK;QACL,cAAc;IAAA;AAGpB;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,sBAAsB,KAAA,EAAgB;IAC7C,OAAO,UAAU,MAAM,UAAU,KAAA;AACnC;AAEA,SAAS,mBAAmB,cAAA,EAA0C;IACpE,MAAM,yBAAqB,uMAAA,EAAe,cAAc;IACxD,MAAM,YAAkB,uKAAA,CAAO,EAAE;IACjC,MAAM,WAAiB,uKAAA,CAAO,CAAC;IAE/B,MAAM,wBAA8B,4KAAA;iEAClC,CAAC,QAAgB;YACf,MAAM,SAAS,UAAU,OAAA,GAAU;YACnC,mBAAmB,MAAM;YAEzB,CAAC,SAAS,aAAa,KAAA,EAAe;gBACpC,UAAU,OAAA,GAAU;gBACpB,OAAO,YAAA,CAAa,SAAS,OAAO;gBAEpC,IAAI,UAAU,GAAI,CAAA,SAAS,OAAA,GAAU,OAAO,UAAA;0FAAW,IAAM,aAAa,EAAE;yFAAG,GAAI;YACrF,CAAA,EAAG,MAAM;QACX;gEACA;QAAC,kBAAkB;KAAA;IAGrB,MAAM,iBAAuB,4KAAA;0DAAY,MAAM;YAC7C,UAAU,OAAA,GAAU;YACpB,OAAO,YAAA,CAAa,SAAS,OAAO;QACtC;yDAAG,CAAC,CAAC;IAEC,0KAAA;wCAAU,MAAM;YACpB;gDAAO,IAAM,OAAO,YAAA,CAAa,SAAS,OAAO;;QACnD;uCAAG,CAAC,CAAC;IAEL,OAAO;QAAC;QAAW;QAAuB,cAAc;KAAA;AAC1D;AAmBA,SAAS,aACP,KAAA,EACA,MAAA,EACA,WAAA,EACA;IACA,MAAM,aAAa,OAAO,MAAA,GAAS,KAAK,MAAM,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,CAAC,OAAS,SAAS,MAAA,CAAO,CAAC,CAAC;IAC7F,MAAM,mBAAmB,aAAa,MAAA,CAAO,CAAC,CAAA,GAAK;IACnD,MAAM,mBAAmB,cAAc,MAAM,OAAA,CAAQ,WAAW,IAAI,CAAA;IACpE,IAAI,eAAe,UAAU,OAAO,KAAK,GAAA,CAAI,kBAAkB,CAAC,CAAC;IACjE,MAAM,qBAAqB,iBAAiB,MAAA,KAAW;IACvD,IAAI,mBAAoB,CAAA,eAAe,aAAa,MAAA,CAAO,CAAC,IAAM,MAAM,WAAW;IACnF,MAAM,WAAW,aAAa,IAAA,CAAK,CAAC,OAClC,KAAK,SAAA,CAAU,WAAA,CAAY,EAAE,UAAA,CAAW,iBAAiB,WAAA,CAAY,CAAC;IAExE,OAAO,aAAa,cAAc,WAAW,KAAA;AAC/C;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAMG,QAAO;AACb,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAMC,SAAQ", "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,cAAA,CAAA;YAAgB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAanF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,WAAA,CAAA,CAAA,KAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chevron-up.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/chevron-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]];\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('chevron-up', __iconNode);\n\nexport default ChevronUp;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,gBAAA,CAAA;YAAkB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAarF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAA,CAAA,CAAA,KAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/save.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA6C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA0B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,IAAA,CAAA,CAAA,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAsD,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAuD,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACpF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAa,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC5C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAA,CAAA,CAAA,KAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1688, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/plus.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC3C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,IAAA,CAAA,CAAA,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA8D,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3F;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAA,CAAA,CAAA,KAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 11v6', key: 'nco0om' }],\n  ['path', { d: 'M14 11v6', key: 'outv1u' }],\n  ['path', { d: 'M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6', key: 'miytrc' }],\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2', key: 'e791ji' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTQgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTkgNnYxNGEyIDIgMCAwIDEtMiAySDdhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Ik0zIDZoMTgiIC8+CiAgPHBhdGggZD0iTTggNlY0YTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAydjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA4C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAW,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA0C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzE;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,MAAA,CAAA,CAAA,KAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,QAAc,2KAAA,CAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;gBAKtB;YAHA,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;aAEvD,qBAAA,MAAM,WAAA,cAAN,yCAAA,wBAAA,OAAoB,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "debugId": null}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/%40radix-ui/react-dialog/src/dialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,eAAe,0BAA0B;AAClD,SAAS,aAAa;AACtB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,kBAAkB;AAC3B,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,oBAAoB;AAC7B,SAAS,kBAAkB;AAC3B,SAAS,kBAAkB;AAsDvB,SA2VM,UA3VN,KA2VM,YA3VN;;;;;;;;;;;;;;;;;;AA9CJ,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,OAAI,4LAAA,EAAmB,WAAW;AAc/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAU9F,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EACJ,aAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,QAAQ,IAAA,EACV,GAAI;IACJ,MAAM,aAAmB,uKAAA,CAA0B,IAAI;IACvD,MAAM,aAAmB,uKAAA,CAA6B,IAAI;IAC1D,MAAM,CAAC,MAAM,OAAO,CAAA,GAAI,uNAAA,EAAqB;QAC3C,MAAM;QACN,8DAAa,cAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA;QACA,eAAW,0KAAA,CAAM;QACjB,aAAS,0KAAA,CAAM;QACf,eAAe,8KAAA,CAAM;QACrB;QACA,cAAc;QACd,cAAoB,4KAAA;kCAAY,IAAM;0CAAQ,CAAC,WAAa,CAAC,QAAQ;;iCAAG;YAAC,OAAO;SAAC;QACjF;QAEC;IAAA;AAGP;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB,2KAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,yBAAqB,iMAAA,EAAgB,cAAc,QAAQ,UAAU;IAC3E,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,iBAAc;QACd,iBAAe,QAAQ,IAAA;QACvB,iBAAe,QAAQ,SAAA;QACvB,cAAY,SAAS,QAAQ,IAAI;QAChC,GAAG,YAAA;QACJ,KAAK;QACL,aAAS,uLAAA,EAAqB,MAAM,OAAA,EAAS,QAAQ,YAAY;IAAA;AAGvE;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,aAAa;IAC9F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,eAA4C,CAAC,UAA0C;IAC3F,MAAM,EAAE,aAAA,EAAe,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IAC3D,MAAM,UAAU,iBAAiB,aAAa,aAAa;IAC3D,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QACnC,UAAM,yKAAA,CAAS,GAAA,CAAI,UAAU,CAAC,QAC7B,aAAA,GAAA,IAAA,6KAAA,EAAC,mLAAA,EAAA;gBAAS,SAAS,cAAc,QAAQ,IAAA;gBACvC,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,+KAAA,EAAA;oBAAgB,SAAO;oBAAC;oBACtB,UAAA;gBAAA,CACH;YAAA,CACF,CACD;IAAA,CACH;AAEJ;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAWrB,IAAM,gBAAsB,2KAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OAAO,QAAQ,KAAA,GACb,aAAA,GAAA,IAAA,6KAAA,EAAC,mLAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACvC,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,mBAAA;YAAmB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAC1D,IACE;AACN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,WAAO,iLAAA,EAAW,4BAA4B;AAEpD,IAAM,oBAA0B,2KAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,OAAA,oFAAA;IAAA,gDAAA;IAGE,aAAA,GAAA,IAAA,6KAAA,EAAC,wOAAA,EAAA;QAAa,IAAI;QAAM,gBAAc;QAAC,QAAQ;YAAC,QAAQ,UAAU;SAAA;QAChE,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;YACC,cAAY,SAAS,QAAQ,IAAI;YAChC,GAAG,YAAA;YACJ,KAAK;YAEL,OAAO;gBAAE,eAAe;gBAAQ,GAAG,aAAa,KAAA;YAAM;QAAA;IACxD,CACF;AAEJ;AAOF,IAAM,eAAe;AAWrB,IAAM,gBAAsB,2KAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,mLAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACtC,UAAA,QAAQ,KAAA,GACP,aAAA,GAAA,IAAA,6KAAA,EAAC,oBAAA;YAAoB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc,IAEzD,aAAA,GAAA,IAAA,6KAAA,EAAC,uBAAA;YAAuB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAEhE;AAEJ;AAGF,cAAc,WAAA,GAAc;AAQ5B,IAAM,qBAA2B,2KAAA,CAC/B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,aAAmB,uKAAA,CAAuB,IAAI;IACpD,MAAM,mBAAe,iMAAA,EAAgB,cAAc,QAAQ,UAAA,EAAY,UAAU;IAG3E,0KAAA;wCAAU,MAAM;YACpB,MAAM,UAAU,WAAW,OAAA;YAC3B,IAAI,QAAS,CAAA,WAAO,0KAAA,EAAW,OAAO;QACxC;uCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QAGL,WAAW,QAAQ,IAAA;QACnB,6BAA2B;QAC3B,sBAAkB,uLAAA,EAAqB,MAAM,gBAAA,EAAkB,CAAC,UAAU;gBAExE;YADA,MAAM,cAAA,CAAe;aACrB,8BAAA,QAAQ,UAAA,CAAW,OAAA,cAAnB,kDAAA,4BAA4B,KAAA,CAAM;QACpC,CAAC;QACD,0BAAsB,uLAAA,EAAqB,MAAM,oBAAA,EAAsB,CAAC,UAAU;YAChF,MAAM,gBAAgB,MAAM,MAAA,CAAO,aAAA;YACnC,MAAM,gBAAgB,cAAc,MAAA,KAAW,KAAK,cAAc,OAAA,KAAY;YAC9E,MAAM,eAAe,cAAc,MAAA,KAAW,KAAK;YAInD,IAAI,aAAc,CAAA,MAAM,cAAA,CAAe;QACzC,CAAC;QAGD,oBAAgB,uLAAA,EAAqB,MAAM,cAAA,EAAgB,CAAC,QAC1D,MAAM,cAAA,CAAe;IACvB;AAGN;AAKF,IAAM,wBAA8B,2KAAA,CAClC,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,0BAAgC,uKAAA,CAAO,KAAK;IAClD,MAAM,2BAAiC,uKAAA,CAAO,KAAK;IAEnD,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,kBAAkB,CAAC,UAAU;gBAC3B;aAAA,0BAAA,MAAM,gBAAA,cAAN,8CAAA,6BAAA,OAAyB,KAAK;YAE9B,IAAI,CAAC,MAAM,gBAAA,EAAkB;oBACW;gBAAtC,IAAI,CAAC,wBAAwB,OAAA,CAAS,EAAA,8BAAA,QAAQ,UAAA,CAAW,OAAA,cAAnB,kDAAA,4BAA4B,KAAA,CAAM;gBAExE,MAAM,cAAA,CAAe;YACvB;YAEA,wBAAwB,OAAA,GAAU;YAClC,yBAAyB,OAAA,GAAU;QACrC;QACA,mBAAmB,CAAC,UAAU;gBAC5B;aAAA,2BAAA,MAAM,iBAAA,cAAN,+CAAA,8BAAA,OAA0B,KAAK;YAE/B,IAAI,CAAC,MAAM,gBAAA,EAAkB;gBAC3B,wBAAwB,OAAA,GAAU;gBAClC,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,eAAe;oBACrD,yBAAyB,OAAA,GAAU;gBACrC;YACF;YAKA,MAAM,SAAS,MAAM,MAAA;YACrB,MAAM,iDAAkB,QAAQ,UAAA,CAAW,OAAA,4FAAS,QAAA,CAAS,MAAM;YACnE,IAAI,gBAAiB,CAAA,MAAM,cAAA,CAAe;YAM1C,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,aAAa,yBAAyB,OAAA,EAAS;gBACrF,MAAM,cAAA,CAAe;YACvB;QACF;IAAA;AAGN;AA6BF,IAAM,oBAA0B,2KAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,eAAA,EAAiB,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IACzF,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,aAAmB,uKAAA,CAAuB,IAAI;IACpD,MAAM,mBAAe,iMAAA,EAAgB,cAAc,UAAU;IAI7D,IAAA,gMAAA,CAAe;IAEf,OACE,aAAA,GAAA,IAAA,8KAAA,EAAA,kLAAA,EAAA;QACE,UAAA;YAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,2LAAA,EAAA;gBACC,SAAO;gBACP,MAAI;gBACJ,SAAS;gBACT,kBAAkB;gBAClB,oBAAoB;gBAEpB,UAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,uMAAA,EAAA;oBACC,MAAK;oBACL,IAAI,QAAQ,SAAA;oBACZ,oBAAkB,QAAQ,aAAA;oBAC1B,mBAAiB,QAAQ,OAAA;oBACzB,cAAY,SAAS,QAAQ,IAAI;oBAChC,GAAG,YAAA;oBACJ,KAAK;oBACL,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;gBAAA;YAC7C;YAGA,aAAA,GAAA,IAAA,8KAAA,EAAA,kLAAA,EAAA;gBACE,UAAA;oBAAA,aAAA,GAAA,IAAA,6KAAA,EAAC,cAAA;wBAAa,SAAS,QAAQ,OAAA;oBAAA,CAAS;oBACxC,aAAA,GAAA,IAAA,6KAAA,EAAC,oBAAA;wBAAmB;wBAAwB,eAAe,QAAQ,aAAA;oBAAA,CAAe;iBAAA;YAAA,CACpF;SAAA;IAAA,CAEJ;AAEJ;AAOF,IAAM,aAAa;AAMnB,IAAM,cAAoB,2KAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OAAO,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,EAAA,EAAV;QAAa,IAAI,QAAQ,OAAA;QAAU,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC/E;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,mBAAmB;AAMzB,IAAM,oBAA0B,2KAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,iBAAiB,CAAA,GAAI;IAC/C,MAAM,UAAU,iBAAiB,kBAAkB,aAAa;IAChE,OAAO,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,CAAA,EAAV;QAAY,IAAI,QAAQ,aAAA;QAAgB,GAAG,gBAAA;QAAkB,KAAK;IAAA,CAAc;AAC1F;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,aAAa;AAKnB,IAAM,cAAoB,2KAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACJ,GAAG,UAAA;QACJ,KAAK;QACL,aAAS,uLAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,YAAA,CAAa,KAAK,CAAC;IAAA;AAGpF;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,SAAS,IAAA,EAAe;IAC/B,OAAO,OAAO,SAAS;AACzB;AAEA,IAAM,qBAAqB;AAE3B,IAAM,CAAC,iBAAiB,iBAAiB,CAAA,OAAI,uLAAA,EAAc,oBAAoB;IAC7E,aAAa;IACb,WAAW;IACX,UAAU;AACZ,CAAC;AAID,IAAM,eAA4C;QAAC,EAAE,OAAA,CAAQ,CAAA,KAAM;IACjE,MAAM,sBAAsB,kBAAkB,kBAAkB;IAEhE,MAAM,UAAU,WAAK,oBAAoB,WAAW,EAAA,yBAAmB,oBAAoB,SAAS,EAAA,qGAE1E,oBAAoB,SAAS,EAAA,uIAE+C,OAA5B,oBAAoB,QAAQ;IAEhG,0KAAA;kCAAU,MAAM;YACpB,IAAI,SAAS;gBACX,MAAM,WAAW,SAAS,cAAA,CAAe,OAAO;gBAChD,IAAI,CAAC,SAAU,CAAA,QAAQ,KAAA,CAAM,OAAO;YACtC;QACF;iCAAG;QAAC;QAAS,OAAO;KAAC;IAErB,OAAO;AACT;AAEA,IAAM,2BAA2B;AAOjC,IAAM,qBAAwD;QAAC,EAAE,UAAA,EAAY,aAAA,CAAc,CAAA,KAAM;IAC/F,MAAM,4BAA4B,kBAAkB,wBAAwB;IAC5E,MAAM,UAAU,yEAAkH,OAArC,0BAA0B,WAAW,EAAA;IAE5H,0KAAA;wCAAU,MAAM;;YACpB,MAAM,iBAAgB,iCAAW,OAAA,4EAAS,YAAA,CAAa,kBAAkB;YAEzE,IAAI,iBAAiB,eAAe;gBAClC,MAAM,iBAAiB,SAAS,cAAA,CAAe,aAAa;gBAC5D,IAAI,CAAC,eAAgB,CAAA,QAAQ,IAAA,CAAK,OAAO;YAC3C;QACF;uCAAG;QAAC;QAAS;QAAY,aAAa;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,QAAQ", "debugId": null}}, {"offset": {"line": 2279, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/x.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('x', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC7C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,KAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 2324, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/copy.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/node_modules/lucide-react/src/icons/copy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '14', height: '14', x: '8', y: '8', rx: '2', ry: '2', key: '17jyea' }],\n  ['path', { d: 'M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2', key: 'zix9uf' }],\n];\n\n/**\n * @component @name Copy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHg9IjgiIHk9IjgiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNNCAxNmMtMS4xIDAtMi0uOS0yLTJWNGMwLTEuMS45LTIgMi0yaDEwYzEuMSAwIDIgLjkgMiAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/copy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Copy = createLucideIcon('copy', __iconNode);\n\nexport default Copy;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,KAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAA,CAAA,GAAQ,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAG,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA;YAAK,CAAA,GAAI,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA2D,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1F;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,IAAA,CAAA,CAAA,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}]}