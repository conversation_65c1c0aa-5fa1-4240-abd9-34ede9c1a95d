{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number) {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(amount / 100) // Convert from cents to rupees\n}\n\nexport function formatDate(date: string | Date) {\n  return new Date(date).toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  })\n}\n\nexport function getRelativeTime(dateString: string) {\n  const date = new Date(dateString)\n  const now = new Date()\n  const diffInMs = now.getTime() - date.getTime()\n  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n\n  if (diffInDays === 0) {\n    return 'Today'\n  } else if (diffInDays === 1) {\n    return 'Yesterday'\n  } else if (diffInDays < 7) {\n    return `${diffInDays} days ago`\n  } else if (diffInDays < 30) {\n    const weeks = Math.floor(diffInDays / 7)\n    return `${weeks} week${weeks > 1 ? 's' : ''} ago`\n  } else if (diffInDays < 365) {\n    const months = Math.floor(diffInDays / 30)\n    return `${months} month${months > 1 ? 's' : ''} ago`\n  } else {\n    const years = Math.floor(diffInDays / 365)\n    return `${years} year${years > 1 ? 's' : ''} ago`\n  }\n}\n\nexport function getStatusColor(status: string) {\n  switch (status) {\n    case 'confirmed':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n    case 'pending':\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'\n    case 'completed':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n    case 'cancelled':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n    case 'in_progress':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n  }\n}\n\nexport function getPriorityColor(priority: string) {\n  switch (priority) {\n    case 'urgent':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n    case 'high':\n      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'\n    case 'medium':\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'\n    case 'low':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n  }\n}\n\nexport function formatTime(timeString: string) {\n  return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-IN', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS,KAAK,+BAA+B;;AACzD;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,gBAAgB,UAAkB;IAChD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;IAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE7D,IAAI,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,eAAe,GAAG;QAC3B,OAAO;IACT,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,GAAG,WAAW,SAAS,CAAC;IACjC,OAAO,IAAI,aAAa,IAAI;QAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,aAAa,KAAK;QAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;QACvC,OAAO,GAAG,OAAO,MAAM,EAAE,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC;IACtD,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,CAAC,WAAW,EAAE,YAAY,EAAE,kBAAkB,CAAC,SAAS;QACtE,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport {\n  LayoutDashboard,\n  Calendar,\n  Users,\n  Stethoscope,\n  MessageSquare,\n  BarChart3,\n  Settings,\n  Bell,\n  FileText,\n  CreditCard,\n  UserCheck,\n  Clock,\n  Star,\n  Mail,\n  Shield,\n  Database,\n  Activity,\n  TrendingUp,\n  Zap\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/',\n    icon: LayoutDashboard,\n    description: 'Overview and analytics'\n  },\n  {\n    name: 'Appointments',\n    href: '/appointments',\n    icon: Calendar,\n    description: 'Manage appointments',\n    children: [\n      { name: 'All Appointments', href: '/appointments', icon: Calendar },\n      { name: 'Calendar View', href: '/appointments/calendar', icon: Clock },\n      { name: 'Pending Approval', href: '/appointments/pending', icon: UserCheck },\n    ]\n  },\n  {\n    name: 'Patients',\n    href: '/patients',\n    icon: Users,\n    description: 'Patient management',\n    children: [\n      { name: 'All Patients', href: '/patients', icon: Users },\n      { name: 'Medical Records', href: '/patients/records', icon: FileText },\n      { name: 'Patient History', href: '/patients/history', icon: Activity },\n    ]\n  },\n  {\n    name: 'Services',\n    href: '/services',\n    icon: Stethoscope,\n    description: 'Dental services',\n    children: [\n      { name: 'All Services', href: '/services', icon: Stethoscope },\n      { name: 'Pricing', href: '/services/pricing', icon: CreditCard },\n      { name: 'Categories', href: '/services/categories', icon: Database },\n    ]\n  },\n  {\n    name: 'Communications',\n    href: '/communications',\n    icon: MessageSquare,\n    description: 'Messages and notifications',\n    children: [\n      { name: 'Contact Forms', href: '/communications/contacts', icon: MessageSquare },\n      { name: 'Notifications', href: '/communications/notifications', icon: Bell },\n      { name: 'Newsletter', href: '/communications/newsletter', icon: Mail },\n      { name: 'Testimonials', href: '/communications/testimonials', icon: Star },\n    ]\n  },\n  {\n    name: 'Reports',\n    href: '/reports',\n    icon: BarChart3,\n    description: 'Analytics and reports',\n    children: [\n      { name: 'Revenue Analytics', href: '/reports/revenue', icon: TrendingUp },\n      { name: 'Appointment Trends', href: '/reports/appointments', icon: Activity },\n      { name: 'Patient Insights', href: '/reports/patients', icon: Users },\n      { name: 'Service Performance', href: '/reports/services', icon: Zap },\n    ]\n  },\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n    description: 'System configuration',\n    children: [\n      { name: 'General', href: '/settings', icon: Settings },\n      { name: 'Admin Users', href: '/settings/users', icon: Shield },\n      { name: 'Clinic Info', href: '/settings/clinic', icon: Database },\n      { name: 'Availability', href: '/settings/availability', icon: Clock },\n    ]\n  },\n]\n\ninterface SidebarProps {\n  className?: string\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <div className={cn('flex h-full w-64 flex-col bg-[#171717] border-r border-[#404040]', className)}>\n      {/* Logo */}\n      <div className=\"flex h-16 items-center border-b border-[#404040] px-6\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 shadow-lg\">\n            <Stethoscope className=\"h-5 w-5 text-white\" />\n          </div>\n          <div className=\"flex flex-col\">\n            <span className=\"text-base font-bold text-white\">DentalCare</span>\n            <span className=\"text-xs text-gray-400\">Admin Dashboard</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 space-y-2 p-4 overflow-y-auto scrollbar-thin\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href || pathname.startsWith(item.href + '/')\n\n          return (\n            <div key={item.name}>\n              <Link\n                href={item.href}\n                className={cn(\n                  'flex items-center gap-3 rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 hover:bg-[#2a2a2a] group',\n                  isActive\n                    ? 'bg-gradient-to-r from-orange-500/20 to-orange-600/20 text-orange-400 border-l-4 border-orange-500'\n                    : 'text-gray-300 hover:text-white'\n                )}\n              >\n                <item.icon className={cn(\n                  \"h-5 w-5 transition-colors\",\n                  isActive ? \"text-orange-400\" : \"text-gray-400 group-hover:text-white\"\n                )} />\n                <div className=\"flex flex-col\">\n                  <span className=\"font-medium\">{item.name}</span>\n                  <span className=\"text-xs text-gray-500 group-hover:text-gray-400\">\n                    {item.description}\n                  </span>\n                </div>\n              </Link>\n              \n              {/* Sub-navigation */}\n              {item.children && isActive && (\n                <div className=\"ml-8 mt-2 space-y-1\">\n                  {item.children.map((child) => {\n                    const isChildActive = pathname === child.href\n                    return (\n                      <Link\n                        key={child.name}\n                        href={child.href}\n                        className={cn(\n                          'flex items-center gap-2 rounded-lg px-3 py-2 text-xs transition-all duration-200 hover:bg-[#2a2a2a] group',\n                          isChildActive\n                            ? 'bg-orange-500/10 text-orange-300 border-l-2 border-orange-500'\n                            : 'text-gray-400 hover:text-gray-200'\n                        )}\n                      >\n                        <child.icon className={cn(\n                          \"h-3 w-3\",\n                          isChildActive ? \"text-orange-300\" : \"text-gray-500 group-hover:text-gray-300\"\n                        )} />\n                        {child.name}\n                      </Link>\n                    )\n                  })}\n                </div>\n              )}\n            </div>\n          )\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex items-center gap-3 rounded-lg bg-muted/50 p-3\">\n          <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium\">\n            A\n          </div>\n          <div className=\"flex flex-col\">\n            <span className=\"text-sm font-medium\">Admin User</span>\n            <span className=\"text-xs text-muted-foreground\"><EMAIL></span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AA2BA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+OAAe;QACrB,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sNAAQ;QACd,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;gBAAiB,MAAM,sNAAQ;YAAC;YAClE;gBAAE,MAAM;gBAAiB,MAAM;gBAA0B,MAAM,6MAAK;YAAC;YACrE;gBAAE,MAAM;gBAAoB,MAAM;gBAAyB,MAAM,6NAAS;YAAC;SAC5E;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAK;QACX,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAgB,MAAM;gBAAa,MAAM,6MAAK;YAAC;YACvD;gBAAE,MAAM;gBAAmB,MAAM;gBAAqB,MAAM,0NAAQ;YAAC;YACrE;gBAAE,MAAM;gBAAmB,MAAM;gBAAqB,MAAM,sNAAQ;YAAC;SACtE;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAW;QACjB,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAgB,MAAM;gBAAa,MAAM,+NAAW;YAAC;YAC7D;gBAAE,MAAM;gBAAW,MAAM;gBAAqB,MAAM,gOAAU;YAAC;YAC/D;gBAAE,MAAM;gBAAc,MAAM;gBAAwB,MAAM,sNAAQ;YAAC;SACpE;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yOAAa;QACnB,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAiB,MAAM;gBAA4B,MAAM,yOAAa;YAAC;YAC/E;gBAAE,MAAM;gBAAiB,MAAM;gBAAiC,MAAM,0MAAI;YAAC;YAC3E;gBAAE,MAAM;gBAAc,MAAM;gBAA8B,MAAM,0MAAI;YAAC;YACrE;gBAAE,MAAM;gBAAgB,MAAM;gBAAgC,MAAM,0MAAI;YAAC;SAC1E;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAS;QACf,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAqB,MAAM;gBAAoB,MAAM,gOAAU;YAAC;YACxE;gBAAE,MAAM;gBAAsB,MAAM;gBAAyB,MAAM,sNAAQ;YAAC;YAC5E;gBAAE,MAAM;gBAAoB,MAAM;gBAAqB,MAAM,6MAAK;YAAC;YACnE;gBAAE,MAAM;gBAAuB,MAAM;gBAAqB,MAAM,uMAAG;YAAC;SACrE;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sNAAQ;QACd,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;gBAAa,MAAM,sNAAQ;YAAC;YACrD;gBAAE,MAAM;gBAAe,MAAM;gBAAmB,MAAM,gNAAM;YAAC;YAC7D;gBAAE,MAAM;gBAAe,MAAM;gBAAoB,MAAM,sNAAQ;YAAC;YAChE;gBAAE,MAAM;gBAAgB,MAAM;gBAA0B,MAAM,6MAAK;YAAC;SACrE;IACH;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,WAAW,IAAA,iJAAW;IAE5B,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,oEAAoE;;0BAErF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,+NAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAiC;;;;;;8CACjD,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oBAE3E,qBACE,8OAAC;;0CACC,8OAAC,uKAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,IAAA,yHAAE,EACX,yHACA,WACI,sGACA;;kDAGN,8OAAC,KAAK,IAAI;wCAAC,WAAW,IAAA,yHAAE,EACtB,6BACA,WAAW,oBAAoB;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DACb,KAAK,WAAW;;;;;;;;;;;;;;;;;;4BAMtB,KAAK,QAAQ,IAAI,0BAChB,8OAAC;gCAAI,WAAU;0CACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;oCAClB,MAAM,gBAAgB,aAAa,MAAM,IAAI;oCAC7C,qBACE,8OAAC,uKAAI;wCAEH,MAAM,MAAM,IAAI;wCAChB,WAAW,IAAA,yHAAE,EACX,6GACA,gBACI,kEACA;;0DAGN,8OAAC,MAAM,IAAI;gDAAC,WAAW,IAAA,yHAAE,EACvB,WACA,gBAAgB,oBAAoB;;;;;;4CAErC,MAAM,IAAI;;uCAbN,MAAM,IAAI;;;;;gCAgBrB;;;;;;;uBA7CI,KAAK,IAAI;;;;;gBAkDvB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA+G;;;;;;sCAG9H,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5D", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,mNAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // Additional props can be added here if needed\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAOA,MAAM,sBAAQ,mNAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,IAAA,yHAAE,EACX,qXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,IAAA,uKAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,oLAA0B;AAE/C,MAAM,sBAAsB,uLAA6B;AAEzD,MAAM,oBAAoB,qLAA2B;AAErD,MAAM,qBAAqB,sLAA4B;AAEvD,MAAM,kBAAkB,mLAAyB;AAEjD,MAAM,yBAAyB,0LAAgC;AAE/D,MAAM,uCAAyB,mNAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,0LAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sOAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,0LAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,mNAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,0LAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,0LAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,mNAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,sLAA4B;kBAC3B,cAAA,8OAAC,uLAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,yHAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,uLAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,mNAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oLAA0B;QACzB,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,oLAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,mNAAgB,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4LAAkC;QACjC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6LAAmC;8BAClC,cAAA,8OAAC,6MAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4LAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,mNAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,yLAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6LAAmC;8BAClC,cAAA,8OAAC,gNAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,yLAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,mNAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,qLAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,qLAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,mNAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,yLAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,yLAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      user_profiles: {\n        Row: {\n          id: string\n          created_at: string\n          updated_at: string\n          email: string\n          full_name: string | null\n          phone: string | null\n          date_of_birth: string | null\n          address: string | null\n          emergency_contact: string | null\n          medical_history: string | null\n          allergies: string | null\n          avatar_url: string | null\n        }\n        Insert: {\n          id: string\n          created_at?: string\n          updated_at?: string\n          email: string\n          full_name?: string | null\n          phone?: string | null\n          date_of_birth?: string | null\n          address?: string | null\n          emergency_contact?: string | null\n          medical_history?: string | null\n          allergies?: string | null\n          avatar_url?: string | null\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          email?: string\n          full_name?: string | null\n          phone?: string | null\n          date_of_birth?: string | null\n          address?: string | null\n          emergency_contact?: string | null\n          medical_history?: string | null\n          allergies?: string | null\n          avatar_url?: string | null\n        }\n      }\n      appointments: {\n        Row: {\n          id: string\n          created_at: string\n          updated_at: string\n          user_id: string | null\n          name: string\n          email: string\n          phone: string\n          service: string\n          preferred_date: string\n          preferred_time: string\n          appointment_date: string | null\n          duration: number | null\n          price: number | null\n          message: string | null\n          status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          payment_status: 'unpaid' | 'partial' | 'paid' | 'refunded'\n          payment_amount: number\n          notes: string | null\n          reminder_sent: boolean\n          confirmed_at: string | null\n          completed_at: string | null\n          cancelled_at: string | null\n          cancellation_reason: string | null\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          user_id?: string | null\n          name: string\n          email: string\n          phone: string\n          service: string\n          preferred_date: string\n          preferred_time: string\n          appointment_date?: string | null\n          duration?: number | null\n          price?: number | null\n          message?: string | null\n          status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          payment_status?: 'unpaid' | 'partial' | 'paid' | 'refunded'\n          payment_amount?: number\n          notes?: string | null\n          reminder_sent?: boolean\n          confirmed_at?: string | null\n          completed_at?: string | null\n          cancelled_at?: string | null\n          cancellation_reason?: string | null\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          user_id?: string | null\n          name?: string\n          email?: string\n          phone?: string\n          service?: string\n          preferred_date?: string\n          preferred_time?: string\n          appointment_date?: string | null\n          duration?: number | null\n          price?: number | null\n          message?: string | null\n          status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          payment_status?: 'unpaid' | 'partial' | 'paid' | 'refunded'\n          payment_amount?: number\n          notes?: string | null\n          reminder_sent?: boolean\n          confirmed_at?: string | null\n          completed_at?: string | null\n          cancelled_at?: string | null\n          cancellation_reason?: string | null\n        }\n      }\n      services: {\n        Row: {\n          id: string\n          created_at: string\n          title: string\n          description: string\n          price: number\n          duration: string\n          benefit: string\n          image_url: string | null\n          is_popular: boolean\n          is_active: boolean\n          category: string\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          title: string\n          description: string\n          price: number\n          duration: string\n          benefit: string\n          image_url?: string | null\n          is_popular?: boolean\n          is_active?: boolean\n          category: string\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          title?: string\n          description?: string\n          price?: number\n          duration?: string\n          benefit?: string\n          image_url?: string | null\n          is_popular?: boolean\n          is_active?: boolean\n          category?: string\n        }\n      }\n      contact_forms: {\n        Row: {\n          id: string\n          created_at: string\n          name: string\n          email: string\n          phone: string | null\n          subject: string\n          message: string\n          status: 'new' | 'replied' | 'resolved'\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          name: string\n          email: string\n          phone?: string | null\n          subject: string\n          message: string\n          status?: 'new' | 'replied' | 'resolved'\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          name?: string\n          email?: string\n          phone?: string | null\n          subject?: string\n          message?: string\n          status?: 'new' | 'replied' | 'resolved'\n        }\n      }\n      testimonials: {\n        Row: {\n          id: string\n          created_at: string\n          name: string\n          title: string\n          quote: string\n          rating: number | null\n          avatar_url: string | null\n          is_featured: boolean\n          is_approved: boolean\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          name: string\n          title: string\n          quote: string\n          rating?: number | null\n          avatar_url?: string | null\n          is_featured?: boolean\n          is_approved?: boolean\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          name?: string\n          title?: string\n          quote?: string\n          rating?: number | null\n          avatar_url?: string | null\n          is_featured?: boolean\n          is_approved?: boolean\n        }\n      }\n      newsletter_subscribers: {\n        Row: {\n          id: string\n          created_at: string\n          email: string\n          name: string | null\n          is_active: boolean\n          subscribed_at: string\n          unsubscribed_at: string | null\n          source: string\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          email: string\n          name?: string | null\n          is_active?: boolean\n          subscribed_at?: string\n          unsubscribed_at?: string | null\n          source?: string\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          email?: string\n          name?: string | null\n          is_active?: boolean\n          subscribed_at?: string\n          unsubscribed_at?: string | null\n          source?: string\n        }\n      }\n      medical_records: {\n        Row: {\n          id: string\n          created_at: string\n          updated_at: string\n          patient_id: string\n          appointment_id: string | null\n          record_type: 'consultation' | 'treatment' | 'prescription' | 'lab_result' | 'x_ray' | 'diagnosis' | 'follow_up'\n          title: string\n          description: string | null\n          diagnosis: string | null\n          treatment: string | null\n          medications: any | null\n          vital_signs: any | null\n          notes: string | null\n          attachments: any | null\n          created_by: string | null\n          is_confidential: boolean\n          tags: string[] | null\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          patient_id: string\n          appointment_id?: string | null\n          record_type: 'consultation' | 'treatment' | 'prescription' | 'lab_result' | 'x_ray' | 'diagnosis' | 'follow_up'\n          title: string\n          description?: string | null\n          diagnosis?: string | null\n          treatment?: string | null\n          medications?: any | null\n          vital_signs?: any | null\n          notes?: string | null\n          attachments?: any | null\n          created_by?: string | null\n          is_confidential?: boolean\n          tags?: string[] | null\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          patient_id?: string\n          appointment_id?: string | null\n          record_type?: 'consultation' | 'treatment' | 'prescription' | 'lab_result' | 'x_ray' | 'diagnosis' | 'follow_up'\n          title?: string\n          description?: string | null\n          diagnosis?: string | null\n          treatment?: string | null\n          medications?: any | null\n          vital_signs?: any | null\n          notes?: string | null\n          attachments?: any | null\n          created_by?: string | null\n          is_confidential?: boolean\n          tags?: string[] | null\n        }\n      }\n    }\n  }\n}\n\nexport type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']\n\n// Type aliases for easier use\nexport type Patient = Tables<'user_profiles'> & {\n  // Medical information from appointments\n  medical_history?: string\n  allergies?: string\n  current_medications?: string\n  previous_dental_work?: string\n  dental_concerns?: string\n  emergency_contact?: string\n  emergency_contact_phone?: string\n  emergency_contact_relationship?: string\n  has_insurance?: string\n  insurance_provider?: string\n  insurance_policy_number?: string\n  last_visit?: string\n\n  // Appointment statistics\n  total_appointments?: number\n  completed_appointments?: number\n  pending_appointments?: number\n  confirmed_appointments?: number\n  cancelled_appointments?: number\n}\nexport type Appointment = Tables<'appointments'>\nexport type Service = Tables<'services'>\nexport type ContactForm = Tables<'contact_forms'>\nexport type Testimonial = Tables<'testimonials'>\nexport type MedicalRecord = Tables<'medical_records'>\n"], "names": [], "mappings": ";;;;AACA;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,IAAA,iMAAmB,EAAC,aAAa", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/contexts/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n\n        // Only redirect on sign out, not on sign in\n        // This prevents automatic redirects when navigating between pages\n        if (event === 'SIGNED_OUT') {\n          router.push('/login')\n          router.refresh()\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router])\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    loading,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;AACA;AALA;;;;;AAaA,MAAM,4BAAc,IAAA,sNAAa,EAA8B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,kIAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,kIAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YACzB,WAAW;YAEX,4CAA4C;YAC5C,kEAAkE;YAClE,IAAI,UAAU,cAAc;gBAC1B,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;KAAO;IAEX,MAAM,UAAU;QACd,MAAM,kIAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Bell, Search, <PERSON>u, <PERSON>, <PERSON>, <PERSON>r, <PERSON><PERSON><PERSON>, LogOut } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useTheme } from 'next-themes'\nimport { useAuth } from '@/contexts/auth-context'\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function Header({ onMenuClick }: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const { theme, setTheme } = useTheme()\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  return (\n    <header className=\"flex h-16 items-center justify-between border-b border-gray-200 dark:border-[#404040] bg-white dark:bg-[#1a1a1a] px-6 shadow-sm\">\n      {/* Left side */}\n      <div className=\"flex items-center gap-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"md:hidden text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-[#2a2a2a]\"\n          onClick={onMenuClick}\n        >\n          <Menu className=\"h-5 w-5\" />\n        </Button>\n\n        <div className=\"relative w-96 max-w-sm\">\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n          <Input\n            placeholder=\"Search patients, appointments...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"pl-10 bg-gray-50 dark:bg-[#2a2a2a] border-gray-200 dark:border-[#404040] text-gray-900 dark:text-gray-100 placeholder:text-gray-500 focus:ring-orange-500 focus:border-orange-500\"\n          />\n        </div>\n      </div>\n\n      {/* Right side */}\n      <div className=\"flex items-center gap-4\">\n        {/* Theme toggle */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n\n        {/* Notifications */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-4 w-4\" />\n              <Badge className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs\">\n                3\n              </Badge>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\" className=\"w-80\">\n            <DropdownMenuLabel>Notifications</DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <div className=\"space-y-2 p-2\">\n              <div className=\"flex items-start gap-3 rounded-lg p-2 hover:bg-accent\">\n                <div className=\"h-2 w-2 rounded-full bg-blue-500 mt-2\" />\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">New appointment request</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    Priya Sharma requested an appointment for tomorrow\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">2 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3 rounded-lg p-2 hover:bg-accent\">\n                <div className=\"h-2 w-2 rounded-full bg-green-500 mt-2\" />\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">Payment received</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    ₹15,000 payment received from Rajesh Kumar\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">1 hour ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3 rounded-lg p-2 hover:bg-accent\">\n                <div className=\"h-2 w-2 rounded-full bg-orange-500 mt-2\" />\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">Appointment reminder</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    Send reminder to Anita Patel for tomorrow&apos;s appointment\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">3 hours ago</p>\n                </div>\n              </div>\n            </div>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem className=\"text-center\">\n              View all notifications\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n\n        {/* User menu */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium\">\n                A\n              </div>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n            <DropdownMenuLabel className=\"font-normal\">\n              <div className=\"flex flex-col space-y-1\">\n                <p className=\"text-sm font-medium leading-none\">\n                  {user?.user_metadata?.full_name || 'Admin User'}\n                </p>\n                <p className=\"text-xs leading-none text-muted-foreground\">\n                  {user?.email || '<EMAIL>'}\n                </p>\n              </div>\n            </DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Profile</span>\n            </DropdownMenuItem>\n            <DropdownMenuItem>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              <span>Settings</span>\n            </DropdownMenuItem>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem onClick={handleSignOut} className=\"text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400\">\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              <span>Log out</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAQA;AACA;AAhBA;;;;;;;;;;AAsBO,SAAS,OAAO,EAAE,WAAW,EAAe;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAA,4JAAQ;IACpC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,8IAAO;IAEjC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,0MAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gNAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,0IAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;;0CAErD,8OAAC,uMAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC,0MAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAI5B,8OAAC,4JAAY;;0CACX,8OAAC,mKAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,4IAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,8OAAC,0MAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC,0IAAK;4CAAC,WAAU;sDAA4D;;;;;;;;;;;;;;;;;0CAKjF,8OAAC,mKAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,8OAAC,iKAAiB;kDAAC;;;;;;kDACnB,8OAAC,qKAAqB;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;kDAInD,8OAAC,qKAAqB;;;;;kDACtB,8OAAC,gKAAgB;wCAAC,WAAU;kDAAc;;;;;;;;;;;;;;;;;;kCAO9C,8OAAC,4JAAY;;0CACX,8OAAC,mKAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,4IAAM;oCAAC,SAAQ;oCAAQ,WAAU;8CAChC,cAAA,8OAAC;wCAAI,WAAU;kDAA+G;;;;;;;;;;;;;;;;0CAKlI,8OAAC,mKAAmB;gCAAC,WAAU;gCAAO,OAAM;gCAAM,UAAU;;kDAC1D,8OAAC,iKAAiB;wCAAC,WAAU;kDAC3B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,MAAM,eAAe,aAAa;;;;;;8DAErC,8OAAC;oDAAE,WAAU;8DACV,MAAM,SAAS;;;;;;;;;;;;;;;;;kDAItB,8OAAC,qKAAqB;;;;;kDACtB,8OAAC,gKAAgB;;0DACf,8OAAC,0MAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,gKAAgB;;0DACf,8OAAC,sNAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qKAAqB;;;;;kDACtB,8OAAC,gKAAgB;wCAAC,SAAS;wCAAe,WAAU;;0DAClD,8OAAC,oNAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/auth/auth-guard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Loader2 } from 'lucide-react'\n\ninterface AuthGuardProps {\n  children: React.ReactNode\n}\n\nexport function AuthGuard({ children }: AuthGuardProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-16 w-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg mb-4\">\n            <Loader2 className=\"h-8 w-8 text-white animate-spin\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Loading...\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Checking authentication status\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show nothing while redirecting to login\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-16 w-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg mb-4\">\n            <Loader2 className=\"h-8 w-8 text-white animate-spin\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Redirecting...\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Please wait while we redirect you to login\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  // User is authenticated, render children\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,8IAAO;IACjC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4NAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,0CAA0C;IAC1C,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4NAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,yCAAyC;IACzC,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { usePathname } from 'next/navigation'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { AuthGuard } from '@/components/auth/auth-guard'\nimport { cn } from '@/lib/utils'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n\n  // Don't show auth guard on login page\n  if (pathname === '/login') {\n    return <>{children}</>\n  }\n\n  return (\n    <AuthGuard>\n    <div className=\"flex h-screen bg-gray-50 dark:bg-[#0f0f0f]\">\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 md:hidden\">\n          <div\n            className=\"absolute inset-0 bg-black/60 backdrop-blur-sm\"\n            onClick={() => setSidebarOpen(false)}\n          />\n          <div className=\"absolute left-0 top-0 h-full shadow-2xl\">\n            <Sidebar />\n          </div>\n        </div>\n      )}\n\n      {/* Main content */}\n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-[#0f0f0f] p-6\">\n          <div className=\"mx-auto max-w-7xl\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n    </AuthGuard>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAaO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,WAAW,IAAA,iJAAW;IAE5B,sCAAsC;IACtC,IAAI,aAAa,UAAU;QACzB,qBAAO;sBAAG;;IACZ;IAEA,qBACE,8OAAC,wJAAS;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kJAAO;;;;;;;;;;gBAIT,6BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,eAAe;;;;;;sCAEhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kJAAO;;;;;;;;;;;;;;;;8BAMd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gJAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAC1C,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 1779, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/theme-wrapper.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ThemeWrapperProps {\n  children: React.ReactNode\n}\n\nexport function ThemeWrapper({ children }: ThemeWrapperProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    // Return a loading state that matches the expected structure\n    return (\n      <div className=\"flex h-screen bg-[#0f0f0f]\">\n        <div className=\"flex h-full w-64 flex-col bg-[#171717] border-r border-[#404040]\">\n          <div className=\"flex h-16 items-center border-b border-[#404040] px-6\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600\">\n                <div className=\"h-5 w-5 bg-white rounded-sm\" />\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-base font-bold text-white\">DentalCare</span>\n                <span className=\"text-xs text-gray-400\">Loading...</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex flex-1 flex-col\">\n          <div className=\"flex h-16 items-center justify-between border-b border-[#404040] bg-[#1a1a1a] px-6\">\n            <div className=\"h-4 w-32 bg-gray-700 rounded animate-pulse\" />\n          </div>\n          <div className=\"flex-1 bg-[#0f0f0f] p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"h-8 w-48 bg-gray-700 rounded animate-pulse\" />\n              <div className=\"h-4 w-96 bg-gray-700 rounded animate-pulse\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,6DAA6D;QAC7D,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAiC;;;;;;sDACjD,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}]}