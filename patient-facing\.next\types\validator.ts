// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../app/about/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/about">> = Specific
  const handler = {} as typeof import("../../app/about/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/auth/login/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/auth/login">> = Specific
  const handler = {} as typeof import("../../app/auth/login/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/auth/signup/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/auth/signup">> = Specific
  const handler = {} as typeof import("../../app/auth/signup/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/auth/verify-email/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/auth/verify-email">> = Specific
  const handler = {} as typeof import("../../app/auth/verify-email/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/book/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/book">> = Specific
  const handler = {} as typeof import("../../app/book/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/contact/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/contact">> = Specific
  const handler = {} as typeof import("../../app/contact/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/dashboard/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard">> = Specific
  const handler = {} as typeof import("../../app/dashboard/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/gallery/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/gallery">> = Specific
  const handler = {} as typeof import("../../app/gallery/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/privacy-policy/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/privacy-policy">> = Specific
  const handler = {} as typeof import("../../app/privacy-policy/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/services/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/services">> = Specific
  const handler = {} as typeof import("../../app/services/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/terms/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/terms">> = Specific
  const handler = {} as typeof import("../../app/terms/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/appointments/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/appointments">> = Specific
  const handler = {} as typeof import("../../app/api/appointments/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/availability/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/availability">> = Specific
  const handler = {} as typeof import("../../app/api/availability/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/contact/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/contact">> = Specific
  const handler = {} as typeof import("../../app/api/contact/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/newsletter/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/newsletter">> = Specific
  const handler = {} as typeof import("../../app/api/newsletter/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/services/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/services">> = Specific
  const handler = {} as typeof import("../../app/api/services/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/testimonials/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/testimonials">> = Specific
  const handler = {} as typeof import("../../app/api/testimonials/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
