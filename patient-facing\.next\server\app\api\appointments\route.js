/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/appointments/route";
exports.ids = ["app/api/appointments/route"];
exports.modules = {

/***/ "(rsc)/./app/api/appointments/route.ts":
/*!***************************************!*\
  !*** ./app/api/appointments/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./lib/supabase.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/external.js\");\n\n\n\n// Validation schema for appointment data\nconst appointmentSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    // Basic Information\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(2, 'Name must be at least 2 characters'),\n    email: zod__WEBPACK_IMPORTED_MODULE_2__.string().email('Invalid email address'),\n    phone: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(10, 'Phone number must be at least 10 digits'),\n    date_of_birth: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(1, 'Date of birth is required'),\n    address: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    // Appointment Details\n    service: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(1, 'Please select a service'),\n    preferred_date: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(1, 'Please select a date'),\n    preferred_time: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(1, 'Please select a time'),\n    message: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    // Medical History\n    medical_history: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    current_medications: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    previous_dental_work: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    dental_concerns: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    // Emergency Contact\n    emergency_contact_name: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(2, 'Emergency contact name is required'),\n    emergency_contact_phone: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(10, 'Emergency contact phone is required'),\n    emergency_contact_relationship: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    // Insurance Information (Optional)\n    has_insurance: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\n        'yes',\n        'no',\n        'unsure'\n    ]).optional(),\n    insurance_provider: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n    insurance_policy_number: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional()\n});\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate the request data\n        const validatedData = appointmentSchema.parse(body);\n        // Check slot availability before creating appointment\n        const availabilityResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3003'}/api/availability?date=${validatedData.preferred_date}&time=${validatedData.preferred_time}`, {\n            method: 'GET'\n        });\n        if (availabilityResponse.ok) {\n            const availabilityData = await availabilityResponse.json();\n            if (!availabilityData.available) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: availabilityData.reason || 'Selected time slot is not available. Please choose a different time.'\n                }, {\n                    status: 409\n                }) // Conflict status\n                ;\n            }\n        }\n        // Create appointment in Supabase\n        const appointment = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.appointmentService.create(validatedData);\n        // TODO: Send confirmation email to patient\n        // TODO: Send notification to clinic staff\n        // TODO: Add to calendar system\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Appointment booked successfully!',\n            appointment: {\n                id: appointment.id,\n                name: appointment.name,\n                service: appointment.service,\n                preferred_date: appointment.preferred_date,\n                preferred_time: appointment.preferred_time,\n                status: appointment.status\n            }\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Appointment booking error:', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid data provided',\n                errors: error.errors.map((err)=>({\n                        field: err.path.join('.'),\n                        message: err.message\n                    }))\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to book appointment. Please try again or call us directly.'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // This endpoint could be used by admin to view appointments\n        // For now, we'll return a simple response\n        const appointments = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.appointmentService.getAll();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            appointments: appointments.map((apt)=>({\n                    id: apt.id,\n                    name: apt.name,\n                    service: apt.service,\n                    preferred_date: apt.preferred_date,\n                    preferred_time: apt.preferred_time,\n                    status: apt.status,\n                    created_at: apt.created_at\n                }))\n        });\n    } catch (error) {\n        console.error('Error fetching appointments:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch appointments'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/appointments/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appointmentService: () => (/* binding */ appointmentService),\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   contactService: () => (/* binding */ contactService),\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   newsletterService: () => (/* binding */ newsletterService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService),\n/* harmony export */   profileService: () => (/* binding */ profileService),\n/* harmony export */   serviceService: () => (/* binding */ serviceService),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testimonialService: () => (/* binding */ testimonialService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n\nconst supabaseUrl = \"https://yuyruqdqlkecsnschziv.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl1eXJ1cWRxbGtlY3Nuc2Noeml2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg3NzY5NTEsImV4cCI6MjA3NDM1Mjk1MX0.vCcS1fALVF4yWdykoWNqaLs2UQhjpOaDbA8G4GYJIxc\" || 0;\n// Function to get Supabase client\nconst getSupabaseClient = ()=>{\n    if (false) {}\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n};\n// Client for server-side operations (with fallback)\nconst supabase = (()=>{\n    try {\n        return getSupabaseClient();\n    } catch  {\n        return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)('https://placeholder.supabase.co', 'placeholder-key');\n    }\n})();\n// Client for browser-side operations with auth\nconst createSupabaseClient = ()=>{\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n};\n// Database functions\nconst appointmentService = {\n    async create (appointmentData) {\n        // Get current user if authenticated\n        const { data: { user } } = await supabase.auth.getUser();\n        try {\n            // First, create or update user profile with personal information\n            if (appointmentData.email) {\n                const profileData = {\n                    email: appointmentData.email,\n                    full_name: appointmentData.name,\n                    phone: appointmentData.phone,\n                    date_of_birth: appointmentData.date_of_birth,\n                    address: appointmentData.address,\n                    updated_at: new Date().toISOString()\n                };\n                // Try to find existing profile by email\n                const { data: existingProfile } = await supabase.from('user_profiles').select('id').eq('email', appointmentData.email).single();\n                if (existingProfile) {\n                    // Update existing profile\n                    await supabase.from('user_profiles').update(profileData).eq('id', existingProfile.id);\n                } else if (user?.id) {\n                    // Create new profile for authenticated user\n                    await userProfileService.createProfile(user.id, profileData);\n                }\n            }\n            // Prepare appointment data (excluding user profile fields)\n            const appointmentInsertData = {\n                name: appointmentData.name,\n                email: appointmentData.email,\n                phone: appointmentData.phone,\n                date_of_birth: appointmentData.date_of_birth,\n                address: appointmentData.address,\n                service: appointmentData.service,\n                preferred_date: appointmentData.preferred_date,\n                preferred_time: appointmentData.preferred_time,\n                message: appointmentData.message,\n                medical_history: appointmentData.medical_history,\n                allergies: appointmentData.allergies,\n                current_medications: appointmentData.current_medications,\n                previous_dental_work: appointmentData.previous_dental_work,\n                dental_concerns: appointmentData.dental_concerns,\n                emergency_contact_name: appointmentData.emergency_contact_name,\n                emergency_contact_phone: appointmentData.emergency_contact_phone,\n                emergency_contact_relationship: appointmentData.emergency_contact_relationship,\n                has_insurance: appointmentData.has_insurance || 'no',\n                insurance_provider: appointmentData.insurance_provider,\n                insurance_policy_number: appointmentData.insurance_policy_number,\n                status: 'pending',\n                user_id: user?.id || null\n            };\n            // Insert appointment\n            const { data, error } = await supabase.from('appointments').insert([\n                appointmentInsertData\n            ]).select().single();\n            if (error) {\n                console.error('Appointment insert error:', error);\n                throw error;\n            }\n            // Create appointment history entry\n            if (data && user?.id) {\n                await this.createHistoryEntry(data.id, user.id, 'created', null, 'pending', 'Appointment created by patient');\n            }\n            return data;\n        } catch (error) {\n            console.error('Error in appointmentService.create:', error);\n            throw error;\n        }\n    },\n    async getAll () {\n        const { data, error } = await supabase.from('appointments').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getByUserId (userId) {\n        const { data, error } = await supabase.from('appointments').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getByEmail (email) {\n        const { data, error } = await supabase.from('appointments').select('*').eq('email', email).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    async getUpcoming (userId) {\n        const { data, error } = await supabase.from('appointments').select('*').eq('user_id', userId).in('status', [\n            'pending',\n            'confirmed'\n        ]).gte('preferred_date', new Date().toISOString().split('T')[0]).order('preferred_date', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getPast (userId) {\n        const { data, error } = await supabase.from('appointments').select('*').eq('user_id', userId).in('status', [\n            'completed',\n            'cancelled',\n            'no_show'\n        ]).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async updateStatus (id, status, notes) {\n        try {\n            console.log(\"Updating appointment status:\", {\n                id,\n                status,\n                notes\n            });\n            const updateData = {\n                status,\n                updated_at: new Date().toISOString()\n            };\n            // Set timestamp based on status\n            if (status === 'confirmed') {\n                updateData.confirmed_at = new Date().toISOString();\n            } else if (status === 'completed') {\n                updateData.completed_at = new Date().toISOString();\n            } else if (status === 'cancelled') {\n                updateData.cancelled_at = new Date().toISOString();\n                if (notes) updateData.cancellation_reason = notes;\n            }\n            // Add notes to the appointment record\n            if (notes && status !== 'cancelled') {\n                updateData.notes = notes;\n            }\n            console.log(\"Update data:\", updateData);\n            const { data, error } = await supabase.from('appointments').update(updateData).eq('id', id).select().single();\n            if (error) {\n                console.error(\"Error updating appointment:\", error);\n                throw error;\n            }\n            console.log(\"Appointment updated successfully:\", data);\n            return data;\n        } catch (error) {\n            console.error(\"Error in updateStatus:\", error);\n            throw error;\n        }\n    },\n    async createHistoryEntry (appointmentId, userId, action, oldStatus, newStatus, notes) {\n        const { error } = await supabase.from('appointment_history').insert([\n            {\n                appointment_id: appointmentId,\n                user_id: userId,\n                action,\n                old_status: oldStatus,\n                new_status: newStatus,\n                notes,\n                changed_by: 'patient'\n            }\n        ]);\n        if (error) console.error('Error creating history entry:', error);\n    },\n    async getHistory (appointmentId) {\n        const { data, error } = await supabase.from('appointment_history').select('*').eq('appointment_id', appointmentId).order('created_at', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    }\n};\nconst contactService = {\n    async create (contact) {\n        const { data, error } = await supabase.from('contact_forms').insert([\n            {\n                ...contact,\n                status: 'new'\n            }\n        ]).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getAll () {\n        const { data, error } = await supabase.from('contact_forms').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\nconst testimonialService = {\n    async getFeatured () {\n        const client = getSupabaseClient();\n        const { data, error } = await client.from('testimonials').select('*').eq('is_featured', true).eq('is_approved', true).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getAll () {\n        const client = getSupabaseClient();\n        const { data, error } = await client.from('testimonials').select('*').eq('is_approved', true).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\nconst serviceService = {\n    async getActive () {\n        const client = getSupabaseClient();\n        const { data, error } = await client.from('services').select('*').eq('is_active', true).order('is_popular', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getPopular () {\n        const client = getSupabaseClient();\n        const { data, error } = await client.from('services').select('*').eq('is_active', true).eq('is_popular', true).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\nconst newsletterService = {\n    async subscribe (email, name) {\n        // Check if email already exists\n        const { data: existing } = await supabase.from('newsletter_subscribers').select('id, is_active').eq('email', email).single();\n        if (existing) {\n            if (existing.is_active) {\n                throw new Error('Email is already subscribed to our newsletter');\n            } else {\n                // Reactivate subscription\n                const { data, error } = await supabase.from('newsletter_subscribers').update({\n                    is_active: true,\n                    subscribed_at: new Date().toISOString(),\n                    unsubscribed_at: null,\n                    name: name || null\n                }).eq('id', existing.id).select().single();\n                if (error) throw error;\n                return data;\n            }\n        }\n        // Create new subscription\n        const { data, error } = await supabase.from('newsletter_subscribers').insert([\n            {\n                email,\n                name: name || null,\n                source: 'website'\n            }\n        ]).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async unsubscribe (email) {\n        const { data, error } = await supabase.from('newsletter_subscribers').update({\n            is_active: false,\n            unsubscribed_at: new Date().toISOString()\n        }).eq('email', email).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getSubscribers () {\n        const { data, error } = await supabase.from('newsletter_subscribers').select('*').eq('is_active', true).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\nconst authService = {\n    async signUp (email, password, fullName) {\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        if (error) throw error;\n        return data;\n    },\n    async signIn (email, password) {\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) throw error;\n        return data;\n    },\n    async signOut () {\n        const { error } = await supabase.auth.signOut();\n        if (error) throw error;\n    },\n    async getCurrentUser () {\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error) throw error;\n        return user;\n    },\n    async resetPassword (email) {\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        if (error) throw error;\n    }\n};\nconst profileService = {\n    async getProfile (userId) {\n        const { data, error } = await supabase.from('user_profiles').select('*').eq('id', userId).single();\n        if (error) throw error;\n        return data;\n    },\n    async updateProfile (userId, updates) {\n        const { data, error } = await supabase.from('user_profiles').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', userId).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async createProfile (userId, profileData) {\n        const { data, error } = await supabase.from('user_profiles').insert([\n            {\n                id: userId,\n                ...profileData,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }\n        ]).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getUserAppointments (userId) {\n        const { data, error } = await supabase.from('appointments').select('*').eq('user_id', userId).order('appointment_date', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    }\n};\nconst notificationService = {\n    async create (notification) {\n        const { data, error } = await supabase.from('notifications').insert([\n            {\n                ...notification,\n                delivery_method: notification.delivery_method || 'in_app'\n            }\n        ]).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getByUserId (userId, unreadOnly = false) {\n        let query = supabase.from('notifications').select('*').eq('user_id', userId);\n        if (unreadOnly) {\n            query = query.eq('is_read', false);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async markAsRead (notificationId) {\n        const { data, error } = await supabase.from('notifications').update({\n            is_read: true\n        }).eq('id', notificationId).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async markAllAsRead (userId) {\n        const { data, error } = await supabase.from('notifications').update({\n            is_read: true\n        }).eq('user_id', userId).eq('is_read', false);\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_hetra_OneDrive_Desktop_Dentist_website_patient_facing_app_api_appointments_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./app/api/appointments/route.ts */ \"(rsc)/./app/api/appointments/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/appointments/route\",\n        pathname: \"/api/appointments\",\n        filename: \"route\",\n        bundlePath: \"app/api/appointments/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\app\\\\api\\\\appointments\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_hetra_OneDrive_Desktop_Dentist_website_patient_facing_app_api_appointments_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/appointments/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/zod","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chetra%5COneDrive%5CDesktop%5CDentist%20website%5Cpatient-facing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();