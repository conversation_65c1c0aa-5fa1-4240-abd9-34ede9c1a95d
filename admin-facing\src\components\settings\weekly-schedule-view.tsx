'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Clock,
  Plus,
  Edit,
  Trash2,
  Copy,
  Users,
  Calendar,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react'

interface TimeSlot {
  id: string
  day_of_week: number
  start_time: string
  end_time: string
  max_appointments: number
  is_available: boolean
  is_recurring: boolean
  notes?: string
  current_appointments?: number
}

interface WeeklyScheduleViewProps {
  slots: TimeSlot[]
  onEditSlot: (slot: TimeSlot) => void
  onDeleteSlot: (slotId: string) => void
  onToggleSlot: (slotId: string, isAvailable: boolean) => void
  onAddSlot: (dayOfWeek: number) => void
  onCopyDay: (fromDay: number, toDay: number) => void
  loading?: boolean
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday', short: 'Sun' },
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' },
]

const CLINIC_HOURS = {
  start: '09:00',
  end: '18:00'
}

export function WeeklyScheduleView({
  slots,
  onEditSlot,
  onDeleteSlot,
  onToggleSlot,
  onAddSlot,
  onCopyDay,
  loading = false
}: WeeklyScheduleViewProps) {
  const [selectedDay, setSelectedDay] = useState<number | null>(null)
  const [showInactive, setShowInactive] = useState(false)

  // Group slots by day of week
  const slotsByDay = DAYS_OF_WEEK.reduce((acc, day) => {
    acc[day.value] = slots
      .filter(slot => slot.day_of_week === day.value && slot.is_recurring)
      .sort((a, b) => a.start_time.localeCompare(b.start_time))
    return acc
  }, {} as Record<number, TimeSlot[]>)

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getSlotDuration = (startTime: string, endTime: string) => {
    const start = new Date(`2000-01-01T${startTime}`)
    const end = new Date(`2000-01-01T${endTime}`)
    const diffMs = end.getTime() - start.getTime()
    const diffHours = diffMs / (1000 * 60 * 60)
    return diffHours
  }

  const isWithinClinicHours = (startTime: string, endTime: string) => {
    return startTime >= CLINIC_HOURS.start && endTime <= CLINIC_HOURS.end
  }

  const getTotalCapacity = (daySlots: TimeSlot[]) => {
    return daySlots
      .filter(slot => slot.is_available)
      .reduce((total, slot) => total + slot.max_appointments, 0)
  }

  const getActiveSlots = (daySlots: TimeSlot[]) => {
    return daySlots.filter(slot => slot.is_available).length
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Weekly Schedule Overview</h3>
          <p className="text-sm text-muted-foreground">
            Clinic Hours: {formatTime(CLINIC_HOURS.start)} - {formatTime(CLINIC_HOURS.end)}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Switch
              checked={showInactive}
              onCheckedChange={setShowInactive}
              id="show-inactive"
            />
            <label htmlFor="show-inactive" className="text-sm">
              Show inactive slots
            </label>
          </div>
        </div>
      </div>

      {/* Weekly Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4">
        {DAYS_OF_WEEK.map((day) => {
          const daySlots = slotsByDay[day.value] || []
          const visibleSlots = showInactive 
            ? daySlots 
            : daySlots.filter(slot => slot.is_available)
          const totalCapacity = getTotalCapacity(daySlots)
          const activeSlots = getActiveSlots(daySlots)

          return (
            <Card 
              key={day.value} 
              className={`${selectedDay === day.value ? 'ring-2 ring-primary' : ''}`}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">
                    {day.label}
                  </CardTitle>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onAddSlot(day.value)}
                    className="h-6 w-6 p-0"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{activeSlots} slots</span>
                  <span>•</span>
                  <span>{totalCapacity} capacity</span>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {visibleSlots.length === 0 ? (
                    <div className="text-center py-4 text-sm text-muted-foreground">
                      No slots configured
                    </div>
                  ) : (
                    visibleSlots.map((slot) => (
                      <div
                        key={slot.id}
                        className={`p-2 rounded-md border ${
                          slot.is_available 
                            ? 'bg-green-50 border-green-200' 
                            : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs font-medium">
                            {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                          </span>
                          <div className="flex items-center gap-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onToggleSlot(slot.id, !slot.is_available)}
                              className="h-5 w-5 p-0"
                            >
                              {slot.is_available ? (
                                <Eye className="h-3 w-3" />
                              ) : (
                                <EyeOff className="h-3 w-3" />
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onEditSlot(slot)}
                              className="h-5 w-5 p-0"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onDeleteSlot(slot.id)}
                              className="h-5 w-5 p-0"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <Badge 
                            variant={slot.is_available ? "default" : "secondary"}
                            className="text-xs"
                          >
                            <Users className="h-2 w-2 mr-1" />
                            {slot.max_appointments}
                          </Badge>
                          {!isWithinClinicHours(slot.start_time, slot.end_time) && (
                            <Badge variant="outline" className="text-xs">
                              Outside hours
                            </Badge>
                          )}
                        </div>
                        {slot.notes && (
                          <p className="text-xs text-muted-foreground mt-1 truncate">
                            {slot.notes}
                          </p>
                        )}
                      </div>
                    ))
                  )}
                </div>
                
                {/* Copy Day Actions */}
                {daySlots.length > 0 && (
                  <div className="mt-3 pt-2 border-t">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        // Show copy options for this day
                        const targetDays = DAYS_OF_WEEK.filter(d => d.value !== day.value)
                        // For now, just copy to next day as example
                        const nextDay = (day.value + 1) % 7
                        onCopyDay(day.value, nextDay)
                      }}
                      className="w-full text-xs"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy Schedule
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Summary Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Weekly Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Total Slots</div>
              <div className="text-muted-foreground">
                {slots.filter(s => s.is_recurring).length}
              </div>
            </div>
            <div>
              <div className="font-medium">Active Slots</div>
              <div className="text-muted-foreground">
                {slots.filter(s => s.is_recurring && s.is_available).length}
              </div>
            </div>
            <div>
              <div className="font-medium">Weekly Capacity</div>
              <div className="text-muted-foreground">
                {slots
                  .filter(s => s.is_recurring && s.is_available)
                  .reduce((total, slot) => total + slot.max_appointments, 0)}
              </div>
            </div>
            <div>
              <div className="font-medium">Coverage</div>
              <div className="text-muted-foreground">
                {DAYS_OF_WEEK.filter(day => 
                  (slotsByDay[day.value] || []).some(slot => slot.is_available)
                ).length}/7 days
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
