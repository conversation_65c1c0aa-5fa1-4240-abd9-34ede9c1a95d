{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,mNAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mNAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mNAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/api-client.ts"], "sourcesContent": ["import { type Appointment, type Patient, type Service } from './supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\nclass ApiClient {\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    const response = await fetch(`/api${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    })\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  // Dashboard\n  async getDashboardStats(): Promise<DashboardStats> {\n    return this.request<DashboardStats>('/dashboard/stats')\n  }\n\n  // Appointments\n  async getAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments')\n  }\n\n  async getAppointment(id: string): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`)\n  }\n\n  async createAppointment(appointment: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(appointment),\n    })\n  }\n\n  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async getPendingAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments/pending')\n  }\n\n  async updatePendingAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/pending?id=${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Patients\n  async getPatients(): Promise<Patient[]> {\n    return this.request<Patient[]>('/patients')\n  }\n\n  async getPatient(id: string): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`)\n  }\n\n  async getPatientAppointments(patientId: string): Promise<any[]> {\n    return this.request<any[]>(`/patients/${patientId}/appointments`)\n  }\n\n  async createPatient(patient: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(patient),\n    })\n  }\n\n  async updatePatient(id: string, updates: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Services\n  async getServices(): Promise<Service[]> {\n    return this.request<Service[]>('/services')\n  }\n\n  async getService(id: string): Promise<Service> {\n    return this.request<Service>(`/services/${id}`)\n  }\n\n  async createService(service: Partial<Service>): Promise<Service> {\n    return this.request<Service>('/services', {\n      method: 'POST',\n      body: JSON.stringify(service),\n    })\n  }\n\n  async updateService(id: string, updates: Partial<Service>): Promise<Service> {\n    return this.request<Service>(`/services/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Reports\n  async getReportsData(dateRange?: string): Promise<any> {\n    const params = dateRange ? `?dateRange=${dateRange}` : ''\n    return this.request<any>(`/reports${params}`)\n  }\n\n  // Medical Records\n  async getMedicalRecords(patientId?: string): Promise<any[]> {\n    const params = patientId ? `?patientId=${patientId}` : ''\n    return this.request<any[]>(`/medical-records${params}`)\n  }\n\n  async getMedicalRecord(id: string): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`)\n  }\n\n  async createMedicalRecord(record: any): Promise<any> {\n    return this.request<any>('/medical-records', {\n      method: 'POST',\n      body: JSON.stringify(record),\n    })\n  }\n\n  async updateMedicalRecord(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Availability Slots\n  async getAvailabilitySlots(): Promise<any[]> {\n    return this.request<any[]>('/availability-slots')\n  }\n\n  async getAvailabilitySlot(id: string): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`)\n  }\n\n  async createAvailabilitySlot(slot: any): Promise<any> {\n    return this.request<any>('/availability-slots', {\n      method: 'POST',\n      body: JSON.stringify(slot),\n    })\n  }\n\n  async updateAvailabilitySlot(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async deleteAvailabilitySlot(id: string): Promise<void> {\n    return this.request<void>(`/availability-slots/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async checkSlotAvailability(date: string, time?: string): Promise<any> {\n    const params = time ? `?date=${date}&time=${time}` : `?date=${date}`\n    return this.request<any>(`/availability-slots/check${params}`)\n  }\n}\n\nexport const api = new ApiClient()\n"], "names": [], "mappings": ";;;;AAaA,MAAM;IACJ,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,MAAM,WAAW,MAAM,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE;YAC9C,SAAS;gBACP,gBAAgB;gBAChB,GAAG,SAAS,OAAO;YACrB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;QAC9D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,oBAA6C;QACjD,OAAO,IAAI,CAAC,OAAO,CAAiB;IACtC;IAEA,eAAe;IACf,MAAM,kBAA0C;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,eAAe,EAAU,EAAwB;QACrD,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,cAAc,EAAE,IAAI;IACxD;IAEA,MAAM,kBAAkB,WAAiC,EAAwB;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAc,iBAAiB;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,OAA6B,EAAwB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,cAAc,EAAE,IAAI,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAyB,EAAU,EAAE,OAA6B,EAAwB;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,yBAAyB,EAAE,IAAI,EAAE;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI;IAChD;IAEA,MAAM,uBAAuB,SAAiB,EAAkB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC;IAClE;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI;IAChD;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,UAAU;IACV,MAAM,eAAe,SAAkB,EAAgB;QACrD,MAAM,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACvD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,QAAQ;IAC9C;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,SAAkB,EAAkB;QAC1D,MAAM,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACvD,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,gBAAgB,EAAE,QAAQ;IACxD;IAEA,MAAM,iBAAiB,EAAU,EAAgB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,iBAAiB,EAAE,IAAI;IACnD;IAEA,MAAM,oBAAoB,MAAW,EAAgB;QACnD,OAAO,IAAI,CAAC,OAAO,CAAM,oBAAoB;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,oBAAoB,EAAU,EAAE,OAAY,EAAgB;QAChE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,iBAAiB,EAAE,IAAI,EAAE;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,uBAAuC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,MAAM,oBAAoB,EAAU,EAAgB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,oBAAoB,EAAE,IAAI;IACtD;IAEA,MAAM,uBAAuB,IAAS,EAAgB;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,uBAAuB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAE,OAAY,EAAgB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,oBAAoB,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAiB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,oBAAoB,EAAE,IAAI,EAAE;YACrD,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,IAAY,EAAE,IAAa,EAAgB;QACrE,MAAM,SAAS,OAAO,CAAC,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM;QACpE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,yBAAyB,EAAE,QAAQ;IAC/D;AACF;AAEO,MAAM,MAAM,IAAI", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/appointments/pending-appointments-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  UserCheck,\n  Clock,\n  Search,\n  RefreshCw,\n  Check,\n  X,\n  Eye,\n  Phone,\n  Mail,\n  Calendar,\n  AlertCircle,\n  Filter\n} from 'lucide-react'\nimport { api } from '@/lib/api-client'\nimport { type Appointment } from '@/lib/supabase'\n\nexport function PendingAppointmentsPage() {\n  const [appointments, setAppointments] = useState<Appointment[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set())\n\n  useEffect(() => {\n    fetchPendingAppointments()\n  }, [])\n\n  const fetchPendingAppointments = async () => {\n    setLoading(true)\n    try {\n      const data = await api.getPendingAppointments()\n      setAppointments(data)\n    } catch (error) {\n      console.error('Error fetching pending appointments:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleApprove = async (appointmentId: string) => {\n    setProcessingIds(prev => new Set(prev).add(appointmentId))\n    try {\n      await api.updatePendingAppointment(appointmentId, {\n        status: 'confirmed',\n        confirmed_at: new Date().toISOString()\n      })\n\n      setAppointments(prev => prev.filter(apt => apt.id !== appointmentId))\n      console.log('Appointment approved:', appointmentId)\n    } catch (error) {\n      console.error('Error approving appointment:', error)\n    } finally {\n      setProcessingIds(prev => {\n        const newSet = new Set(prev)\n        newSet.delete(appointmentId)\n        return newSet\n      })\n    }\n  }\n\n  const handleReject = async (appointmentId: string) => {\n    setProcessingIds(prev => new Set(prev).add(appointmentId))\n    try {\n      await api.updatePendingAppointment(appointmentId, {\n        status: 'cancelled',\n        cancelled_at: new Date().toISOString(),\n        cancellation_reason: 'Rejected by admin'\n      })\n\n      setAppointments(prev => prev.filter(apt => apt.id !== appointmentId))\n      console.log('Appointment rejected:', appointmentId)\n    } catch (error) {\n      console.error('Error rejecting appointment:', error)\n    } finally {\n      setProcessingIds(prev => {\n        const newSet = new Set(prev)\n        newSet.delete(appointmentId)\n        return newSet\n      })\n    }\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'urgent':\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n      case 'normal':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n    }\n  }\n\n\n\n  const filteredAppointments = appointments.filter(appointment =>\n    appointment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    appointment.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    appointment.service.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  const urgentCount = appointments.filter(apt => apt.priority === 'urgent').length\n  const todayCount = appointments.filter(apt => \n    new Date(apt.preferred_date).toDateString() === new Date().toDateString()\n  ).length\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <RefreshCw className=\"h-8 w-8 animate-spin text-muted-foreground\" />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Pending Appointments</h1>\n          <p className=\"text-muted-foreground\">\n            Review and approve appointment requests from patients\n          </p>\n        </div>\n        <Button onClick={fetchPendingAppointments}>\n          <RefreshCw className=\"mr-2 h-4 w-4\" />\n          Refresh\n        </Button>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Pending</CardTitle>\n            <UserCheck className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{appointments.length}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Awaiting approval\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Urgent</CardTitle>\n            <AlertCircle className=\"h-4 w-4 text-red-500\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-red-600\">{urgentCount}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              High priority requests\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Today</CardTitle>\n            <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{todayCount}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Requested for today\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Avg Response</CardTitle>\n            <Clock className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">2.5h</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Average response time\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Appointments Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Appointment Requests</CardTitle>\n          <CardDescription>\n            Review patient appointment requests and take action\n          </CardDescription>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"relative flex-1 max-w-sm\">\n              <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search appointments...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-8\"\n              />\n            </div>\n            <Button variant=\"outline\" size=\"sm\">\n              <Filter className=\"mr-2 h-4 w-4\" />\n              Filter\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Patient</TableHead>\n                <TableHead>Service</TableHead>\n                <TableHead>Preferred Date & Time</TableHead>\n                <TableHead>Priority</TableHead>\n                <TableHead>Source</TableHead>\n                <TableHead>Requested</TableHead>\n                <TableHead className=\"text-right\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredAppointments.map((appointment) => (\n                <TableRow key={appointment.id}>\n                  <TableCell>\n                    <div>\n                      <div className=\"font-medium\">{appointment.name}</div>\n                      <div className=\"text-sm text-muted-foreground flex items-center space-x-2\">\n                        <Mail className=\"h-3 w-3\" />\n                        <span>{appointment.email}</span>\n                      </div>\n                      <div className=\"text-sm text-muted-foreground flex items-center space-x-2\">\n                        <Phone className=\"h-3 w-3\" />\n                        <span>{appointment.phone}</span>\n                      </div>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"font-medium\">{appointment.service}</div>\n                    {appointment.message && (\n                      <div className=\"text-sm text-muted-foreground max-w-[200px] truncate\">\n                        {appointment.message}\n                      </div>\n                    )}\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-2\">\n                      <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n                      <div>\n                        <div className=\"font-medium\">\n                          {new Date(appointment.preferred_date).toLocaleDateString()}\n                        </div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          {appointment.preferred_time}\n                        </div>\n                      </div>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <Badge className={getPriorityColor(appointment.priority)}>\n                      {appointment.priority}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <Badge className=\"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\">\n                      Online\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"text-sm\">\n                      {new Date(appointment.created_at).toLocaleDateString()}\n                    </div>\n                    <div className=\"text-xs text-muted-foreground\">\n                      {new Date(appointment.created_at).toLocaleTimeString()}\n                    </div>\n                  </TableCell>\n                  <TableCell className=\"text-right\">\n                    <div className=\"flex items-center justify-end space-x-2\">\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <Eye className=\"h-4 w-4\" />\n                      </Button>\n                      <Button \n                        variant=\"ghost\" \n                        size=\"sm\"\n                        onClick={() => handleApprove(appointment.id)}\n                        disabled={processingIds.has(appointment.id)}\n                        className=\"text-green-600 hover:text-green-700 hover:bg-green-50\"\n                      >\n                        {processingIds.has(appointment.id) ? (\n                          <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                        ) : (\n                          <Check className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                      <Button \n                        variant=\"ghost\" \n                        size=\"sm\"\n                        onClick={() => handleReject(appointment.id)}\n                        disabled={processingIds.has(appointment.id)}\n                        className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                      >\n                        {processingIds.has(appointment.id) ? (\n                          <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                        ) : (\n                          <X className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n\n          {filteredAppointments.length === 0 && (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <UserCheck className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>No pending appointments found</p>\n              {searchTerm && (\n                <p className=\"text-sm\">Try adjusting your search criteria</p>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AA7BA;;;;;;;;;;AAgCO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAgB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAc,IAAI;IAEpE,IAAA,kNAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,2BAA2B;QAC/B,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,kIAAG,CAAC,sBAAsB;YAC7C,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,iBAAiB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;QAC3C,IAAI;YACF,MAAM,kIAAG,CAAC,wBAAwB,CAAC,eAAe;gBAChD,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;YACtC;YAEA,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACtD,QAAQ,GAAG,CAAC,yBAAyB;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,iBAAiB,CAAA;gBACf,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,iBAAiB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;QAC3C,IAAI;YACF,MAAM,kIAAG,CAAC,wBAAwB,CAAC,eAAe;gBAChD,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;YACvB;YAEA,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACtD,QAAQ,GAAG,CAAC,yBAAyB;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,iBAAiB,CAAA;gBACf,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAIA,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA,cAC/C,YAAY,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,YAAY,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,YAAY,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGnE,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAAU,MAAM;IAChF,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,MACrC,IAAI,KAAK,IAAI,cAAc,EAAE,YAAY,OAAO,IAAI,OAAO,YAAY,IACvE,MAAM;IAER,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,6NAAS;gBAAC,WAAU;;;;;;;;;;;IAG3B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC,4IAAM;wBAAC,SAAS;;0CACf,8OAAC,6NAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAM1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wIAAI;;0CACH,8OAAC,8IAAU;gCAAC,WAAU;;kDACpB,8OAAC,6IAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,6NAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,8OAAC,+IAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,aAAa,MAAM;;;;;;kDACxD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,wIAAI;;0CACH,8OAAC,8IAAU;gCAAC,WAAU;;kDACpB,8OAAC,6IAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,mOAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,8OAAC,+IAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,wIAAI;;0CACH,8OAAC,8IAAU;gCAAC,WAAU;;kDACpB,8OAAC,6IAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,sNAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC,+IAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,wIAAI;;0CACH,8OAAC,8IAAU;gCAAC,WAAU;;kDACpB,8OAAC,6IAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,6MAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,+IAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC,wIAAI;;kCACH,8OAAC,8IAAU;;0CACT,8OAAC,6IAAS;0CAAC;;;;;;0CACX,8OAAC,mJAAe;0CAAC;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gNAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,0IAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAGd,8OAAC,4IAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,gNAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAKzC,8OAAC,+IAAW;;0CACV,8OAAC,0IAAK;;kDACJ,8OAAC,gJAAW;kDACV,cAAA,8OAAC,6IAAQ;;8DACP,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,8OAAC,8IAAS;kDACP,qBAAqB,GAAG,CAAC,CAAC,4BACzB,8OAAC,6IAAQ;;kEACP,8OAAC,8IAAS;kEACR,cAAA,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAe,YAAY,IAAI;;;;;;8EAC9C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;sFAAM,YAAY,KAAK;;;;;;;;;;;;8EAE1B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,6MAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;sFAAM,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;kEAI9B,8OAAC,8IAAS;;0EACR,8OAAC;gEAAI,WAAU;0EAAe,YAAY,OAAO;;;;;;4DAChD,YAAY,OAAO,kBAClB,8OAAC;gEAAI,WAAU;0EACZ,YAAY,OAAO;;;;;;;;;;;;kEAI1B,8OAAC,8IAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sNAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFACZ,IAAI,KAAK,YAAY,cAAc,EAAE,kBAAkB;;;;;;sFAE1D,8OAAC;4EAAI,WAAU;sFACZ,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;kEAKnC,8OAAC,8IAAS;kEACR,cAAA,8OAAC,0IAAK;4DAAC,WAAW,iBAAiB,YAAY,QAAQ;sEACpD,YAAY,QAAQ;;;;;;;;;;;kEAGzB,8OAAC,8IAAS;kEACR,cAAA,8OAAC,0IAAK;4DAAC,WAAU;sEAAgE;;;;;;;;;;;kEAInF,8OAAC,8IAAS;;0EACR,8OAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;0EAEtD,8OAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;;;;;;;kEAGxD,8OAAC,8IAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4IAAM;oEAAC,SAAQ;oEAAQ,MAAK;8EAC3B,cAAA,8OAAC,uMAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,8OAAC,4IAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,cAAc,YAAY,EAAE;oEAC3C,UAAU,cAAc,GAAG,CAAC,YAAY,EAAE;oEAC1C,WAAU;8EAET,cAAc,GAAG,CAAC,YAAY,EAAE,kBAC/B,8OAAC,6NAAS;wEAAC,WAAU;;;;;6FAErB,8OAAC,6MAAK;wEAAC,WAAU;;;;;;;;;;;8EAGrB,8OAAC,4IAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,aAAa,YAAY,EAAE;oEAC1C,UAAU,cAAc,GAAG,CAAC,YAAY,EAAE;oEAC1C,WAAU;8EAET,cAAc,GAAG,CAAC,YAAY,EAAE,kBAC/B,8OAAC,6NAAS;wEAAC,WAAU;;;;;6FAErB,8OAAC,iMAAC;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAjFR,YAAY,EAAE;;;;;;;;;;;;;;;;4BA2FlC,qBAAqB,MAAM,KAAK,mBAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6NAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAE;;;;;;oCACF,4BACC,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC", "debugId": null}}]}