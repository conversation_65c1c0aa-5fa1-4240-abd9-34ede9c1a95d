{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,2KAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,2KAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,2KAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,2KAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,IAAA,4HAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,2KAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/stats-cards.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Calendar,\n  Users,\n  IndianRupee,\n  UserCheck,\n  Clock,\n  TrendingUp,\n  TrendingDown,\n  ArrowUpRight,\n  ArrowDownRight,\n  MessageSquare\n} from 'lucide-react'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface StatsCardsProps {\n  stats: {\n    totalPatients: number\n    totalAppointments: number\n    totalServices: number\n    newMessages: number\n    pendingAppointments: number\n    confirmedAppointments: number\n    completedAppointments: number\n    inProgressAppointments: number\n  } | null\n  loading?: boolean\n}\n\nexport function StatsCards({ stats, loading = false }: StatsCardsProps) {\n  if (loading || !stats) {\n    return (\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n        {[...Array(4)].map((_, i) => (\n          <Card key={i} className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-2\">\n                  <div className=\"h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                  <div className=\"h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                </div>\n                <div className=\"h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse\" />\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    )\n  }\n\n  const cards = [\n    {\n      title: 'Total Patients',\n      value: stats.totalPatients.toString(),\n      description: 'Registered patients',\n      icon: Users,\n      trend: 0, // We'll calculate trends later\n      color: 'text-blue-600'\n    },\n    {\n      title: 'Total Appointments',\n      value: stats.totalAppointments.toString(),\n      description: 'All appointments',\n      icon: Calendar,\n      trend: 0,\n      color: 'text-green-600'\n    },\n    {\n      title: 'Pending Appointments',\n      value: stats.pendingAppointments.toString(),\n      description: 'Awaiting confirmation',\n      icon: Clock,\n      trend: 0,\n      color: 'text-orange-600'\n    },\n    {\n      title: 'New Messages',\n      value: stats.newMessages.toString(),\n      description: 'Unread messages',\n      icon: MessageSquare,\n      trend: 0,\n      color: 'text-purple-600'\n    },\n  ]\n\n  return (\n    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n      {cards.map((card, index) => {\n        const Icon = card.icon\n        const isPositiveTrend = card.trend > 0\n        const TrendIcon = isPositiveTrend ? ArrowUpRight : ArrowDownRight\n        \n        return (\n          <Card\n            key={index}\n            className=\"relative overflow-hidden bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040] hover:shadow-lg transition-all duration-200 group\"\n          >\n            {/* Gradient accent */}\n            <div className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-orange-600\" />\n\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {card.title}\n              </CardTitle>\n              <div className=\"p-2.5 rounded-xl bg-orange-50 dark:bg-orange-900/20 group-hover:scale-110 transition-transform duration-200\">\n                <Icon className=\"h-5 w-5 text-orange-600 dark:text-orange-400\" />\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                {card.value}\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {card.description}\n                </span>\n                <div className=\"flex items-center gap-1\">\n                  <TrendIcon className={`h-3 w-3 ${\n                    isPositiveTrend\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`} />\n                  <span className={`text-xs font-medium ${\n                    isPositiveTrend\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`}>\n                    {Math.abs(card.trend)}%\n                  </span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAgCO,SAAS,WAAW,KAA2C;QAA3C,EAAE,KAAK,EAAE,UAAU,KAAK,EAAmB,GAA3C;IACzB,IAAI,WAAW,CAAC,OAAO;QACrB,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,2IAAI;oBAAS,WAAU;8BACtB,cAAA,6LAAC,kJAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;mBAPV;;;;;;;;;;IAcnB;IAEA,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,aAAa;YACb,MAAM,gNAAK;YACX,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,iBAAiB,CAAC,QAAQ;YACvC,aAAa;YACb,MAAM,yNAAQ;YACd,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,mBAAmB,CAAC,QAAQ;YACzC,aAAa;YACb,MAAM,gNAAK;YACX,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW,CAAC,QAAQ;YACjC,aAAa;YACb,MAAM,4OAAa;YACnB,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,kBAAkB,KAAK,KAAK,GAAG;YACrC,MAAM,YAAY,kBAAkB,6OAAY,GAAG,mPAAc;YAEjE,qBACE,6LAAC,2IAAI;gBAEH,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC,iJAAU;wBAAC,WAAU;;0CACpB,6LAAC,gJAAS;gCAAC,WAAU;0CAClB,KAAK,KAAK;;;;;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;kCAGpB,6LAAC,kJAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK;;;;;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,KAAK,WAAW;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAU,WAAW,AAAC,WAItB,OAHC,kBACI,uCACA;;;;;;0DAEN,6LAAC;gDAAK,WAAW,AAAC,uBAIjB,OAHC,kBACI,uCACA;;oDAEH,KAAK,GAAG,CAAC,KAAK,KAAK;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;eAjCzB;;;;;QAwCX;;;;;;AAGN;KA5GgB", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/revenue-chart.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  AreaChart,\n  Area\n} from 'recharts'\nimport { formatCurrency } from '@/lib/utils'\n\n// Mock data for revenue chart\nconst revenueData = [\n  { month: 'Jan', revenue: 185000, appointments: 45 },\n  { month: 'Feb', revenue: 220000, appointments: 52 },\n  { month: 'Mar', revenue: 195000, appointments: 48 },\n  { month: 'Apr', revenue: 275000, appointments: 65 },\n  { month: 'May', revenue: 310000, appointments: 72 },\n  { month: 'Jun', revenue: 285000, appointments: 68 },\n  { month: 'Jul', revenue: 245000, appointments: 58 },\n]\n\nexport function RevenueChart() {\n  return (\n    <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n      <CardHeader>\n        <CardTitle className=\"text-gray-900 dark:text-white\">Revenue Overview</CardTitle>\n        <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n          Monthly revenue and appointment trends\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"h-[300px]\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <AreaChart data={revenueData}>\n              <defs>\n                <linearGradient id=\"revenueGradient\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                  <stop offset=\"5%\" stopColor=\"hsl(var(--primary))\" stopOpacity={0.3} />\n                  <stop offset=\"95%\" stopColor=\"hsl(var(--primary))\" stopOpacity={0} />\n                </linearGradient>\n              </defs>\n              <CartesianGrid strokeDasharray=\"3 3\" className=\"stroke-muted\" />\n              <XAxis \n                dataKey=\"month\" \n                className=\"text-xs fill-muted-foreground\"\n                axisLine={false}\n                tickLine={false}\n              />\n              <YAxis \n                className=\"text-xs fill-muted-foreground\"\n                axisLine={false}\n                tickLine={false}\n                tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}K`}\n              />\n              <Tooltip\n                content={({ active, payload, label }) => {\n                  if (active && payload && payload.length) {\n                    return (\n                      <div className=\"rounded-lg border bg-background p-3 shadow-md\">\n                        <p className=\"font-medium\">{label}</p>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Revenue: {formatCurrency(payload[0].value as number)}\n                        </p>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Appointments: {payload[0].payload.appointments}\n                        </p>\n                      </div>\n                    )\n                  }\n                  return null\n                }}\n              />\n              <Area\n                type=\"monotone\"\n                dataKey=\"revenue\"\n                stroke=\"hsl(var(--primary))\"\n                strokeWidth={2}\n                fill=\"url(#revenueGradient)\"\n              />\n            </AreaChart>\n          </ResponsiveContainer>\n        </div>\n        \n        {/* Summary Stats */}\n        <div className=\"mt-4 grid grid-cols-3 gap-4 border-t pt-4\">\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-green-600\">\n              {formatCurrency(revenueData.reduce((sum, item) => sum + item.revenue, 0))}\n            </p>\n            <p className=\"text-xs text-muted-foreground\">Total Revenue</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-blue-600\">\n              {formatCurrency(revenueData[revenueData.length - 1].revenue)}\n            </p>\n            <p className=\"text-xs text-muted-foreground\">This Month</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-purple-600\">\n              {formatCurrency(\n                revenueData.reduce((sum, item) => sum + item.revenue, 0) / revenueData.length\n              )}\n            </p>\n            <p className=\"text-xs text-muted-foreground\">Average</p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AAgBA,8BAA8B;AAC9B,MAAM,cAAc;IAClB;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;CACnD;AAEM,SAAS;IACd,qBACE,6LAAC,2IAAI;QAAC,WAAU;;0BACd,6LAAC,iJAAU;;kCACT,6LAAC,gJAAS;wBAAC,WAAU;kCAAgC;;;;;;kCACrD,6LAAC,sJAAe;wBAAC,WAAU;kCAAmC;;;;;;;;;;;;0BAIhE,6LAAC,kJAAW;;kCACV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAmB;4BAAC,OAAM;4BAAO,QAAO;sCACvC,cAAA,6LAAC,qKAAS;gCAAC,MAAM;;kDACf,6LAAC;kDACC,cAAA,6LAAC;4CAAe,IAAG;4CAAkB,IAAG;4CAAI,IAAG;4CAAI,IAAG;4CAAI,IAAG;;8DAC3D,6LAAC;oDAAK,QAAO;oDAAK,WAAU;oDAAsB,aAAa;;;;;;8DAC/D,6LAAC;oDAAK,QAAO;oDAAM,WAAU;oDAAsB,aAAa;;;;;;;;;;;;;;;;;kDAGpE,6LAAC,iLAAa;wCAAC,iBAAgB;wCAAM,WAAU;;;;;;kDAC/C,6LAAC,iKAAK;wCACJ,SAAQ;wCACR,WAAU;wCACV,UAAU;wCACV,UAAU;;;;;;kDAEZ,6LAAC,iKAAK;wCACJ,WAAU;wCACV,UAAU;wCACV,UAAU;wCACV,eAAe,CAAC,QAAU,AAAC,IAA6B,OAA1B,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAG;;;;;;kDAE1D,6LAAC,qKAAO;wCACN,SAAS;gDAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;4CAClC,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;gDACvC,qBACE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,6LAAC;4DAAE,WAAU;;gEAAgC;gEACjC,IAAA,wIAAc,EAAC,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;sEAE3C,6LAAC;4DAAE,WAAU;;gEAAgC;gEAC5B,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY;;;;;;;;;;;;;4CAItD;4CACA,OAAO;wCACT;;;;;;kDAEF,6LAAC,+JAAI;wCACH,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,aAAa;wCACb,MAAK;;;;;;;;;;;;;;;;;;;;;;kCAOb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,IAAA,wIAAc,EAAC,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,OAAO,EAAE;;;;;;kDAExE,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAE/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,IAAA,wIAAc,EAAC,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,OAAO;;;;;;kDAE7D,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAE/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,IAAA,wIAAc,EACb,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,OAAO,EAAE,KAAK,YAAY,MAAM;;;;;;kDAGjF,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzD;KAvFgB", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/appointment-chart.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from '@/components/ui/card'\nimport { \n  <PERSON><PERSON>hart, \n  Bar, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>,\n  Legend\n} from 'recharts'\n\n// Mock data for appointment status distribution\nconst appointmentStatusData = [\n  { name: 'Confirmed', value: 45, color: '#22c55e' },\n  { name: 'Pending', value: 12, color: '#f59e0b' },\n  { name: 'Completed', value: 78, color: '#3b82f6' },\n  { name: 'Cancelled', value: 8, color: '#ef4444' },\n]\n\n// Mock data for weekly appointments\nconst weeklyAppointments = [\n  { day: 'Mon', appointments: 12, completed: 10 },\n  { day: 'Tue', appointments: 15, completed: 13 },\n  { day: 'Wed', appointments: 8, completed: 7 },\n  { day: 'Thu', appointments: 18, completed: 16 },\n  { day: 'Fri', appointments: 22, completed: 20 },\n  { day: 'Sat', appointments: 16, completed: 14 },\n  { day: 'Sun', appointments: 6, completed: 5 },\n]\n\nexport function AppointmentChart() {\n  return (\n    <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n      <CardHeader>\n        <CardTitle className=\"text-gray-900 dark:text-white\">Appointment Analytics</CardTitle>\n        <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n          Weekly trends and status distribution\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-6\">\n          {/* Weekly Bar Chart */}\n          <div className=\"h-[200px]\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <BarChart data={weeklyAppointments}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"stroke-muted\" />\n                <XAxis \n                  dataKey=\"day\" \n                  className=\"text-xs fill-muted-foreground\"\n                  axisLine={false}\n                  tickLine={false}\n                />\n                <YAxis \n                  className=\"text-xs fill-muted-foreground\"\n                  axisLine={false}\n                  tickLine={false}\n                />\n                <Tooltip\n                  content={({ active, payload, label }) => {\n                    if (active && payload && payload.length) {\n                      return (\n                        <div className=\"rounded-lg border bg-background p-3 shadow-md\">\n                          <p className=\"font-medium\">{label}</p>\n                          <p className=\"text-sm text-blue-600\">\n                            Scheduled: {payload[0].value}\n                          </p>\n                          <p className=\"text-sm text-green-600\">\n                            Completed: {payload[1].value}\n                          </p>\n                        </div>\n                      )\n                    }\n                    return null\n                  }}\n                />\n                <Bar \n                  dataKey=\"appointments\" \n                  fill=\"hsl(var(--primary))\" \n                  radius={[2, 2, 0, 0]}\n                  opacity={0.7}\n                />\n                <Bar \n                  dataKey=\"completed\" \n                  fill=\"hsl(var(--primary))\" \n                  radius={[2, 2, 0, 0]}\n                />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Status Distribution */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"h-[120px] w-[120px]\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <PieChart>\n                  <Pie\n                    data={appointmentStatusData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    innerRadius={30}\n                    outerRadius={50}\n                    paddingAngle={2}\n                    dataKey=\"value\"\n                  >\n                    {appointmentStatusData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color} />\n                    ))}\n                  </Pie>\n                  <Tooltip\n                    content={({ active, payload }) => {\n                      if (active && payload && payload.length) {\n                        return (\n                          <div className=\"rounded-lg border bg-background p-2 shadow-md\">\n                            <p className=\"text-sm font-medium\">\n                              {payload[0].payload.name}: {payload[0].value}\n                            </p>\n                          </div>\n                        )\n                      }\n                      return null\n                    }}\n                  />\n                </PieChart>\n              </ResponsiveContainer>\n            </div>\n            \n            <div className=\"flex-1 space-y-2 pl-4\">\n              {appointmentStatusData.map((item, index) => (\n                <div key={index} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <div \n                      className=\"h-3 w-3 rounded-full\" \n                      style={{ backgroundColor: item.color }}\n                    />\n                    <span className=\"text-sm text-muted-foreground\">\n                      {item.name}\n                    </span>\n                  </div>\n                  <span className=\"text-sm font-medium\">{item.value}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiBA,gDAAgD;AAChD,MAAM,wBAAwB;IAC5B;QAAE,MAAM;QAAa,OAAO;QAAI,OAAO;IAAU;IACjD;QAAE,MAAM;QAAW,OAAO;QAAI,OAAO;IAAU;IAC/C;QAAE,MAAM;QAAa,OAAO;QAAI,OAAO;IAAU;IACjD;QAAE,MAAM;QAAa,OAAO;QAAG,OAAO;IAAU;CACjD;AAED,oCAAoC;AACpC,MAAM,qBAAqB;IACzB;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAG,WAAW;IAAE;IAC5C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAG,WAAW;IAAE;CAC7C;AAEM,SAAS;IACd,qBACE,6LAAC,2IAAI;QAAC,WAAU;;0BACd,6LAAC,iJAAU;;kCACT,6LAAC,gJAAS;wBAAC,WAAU;kCAAgC;;;;;;kCACrD,6LAAC,sJAAe;wBAAC,WAAU;kCAAmC;;;;;;;;;;;;0BAIhE,6LAAC,kJAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,6LAAC,mKAAQ;oCAAC,MAAM;;sDACd,6LAAC,iLAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,6LAAC,iKAAK;4CACJ,SAAQ;4CACR,WAAU;4CACV,UAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC,iKAAK;4CACJ,WAAU;4CACV,UAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC,qKAAO;4CACN,SAAS;oDAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;gDAClC,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;oDACvC,qBACE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAe;;;;;;0EAC5B,6LAAC;gEAAE,WAAU;;oEAAwB;oEACvB,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;0EAE9B,6LAAC;gEAAE,WAAU;;oEAAyB;oEACxB,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;gDAIpC;gDACA,OAAO;4CACT;;;;;;sDAEF,6LAAC,6JAAG;4CACF,SAAQ;4CACR,MAAK;4CACL,QAAQ;gDAAC;gDAAG;gDAAG;gDAAG;6CAAE;4CACpB,SAAS;;;;;;sDAEX,6LAAC,6JAAG;4CACF,SAAQ;4CACR,MAAK;4CACL,QAAQ;gDAAC;gDAAG;gDAAG;gDAAG;6CAAE;;;;;;;;;;;;;;;;;;;;;;sCAO5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAmB;wCAAC,OAAM;wCAAO,QAAO;kDACvC,cAAA,6LAAC,mKAAQ;;8DACP,6LAAC,yJAAG;oDACF,MAAM;oDACN,IAAG;oDACH,IAAG;oDACH,aAAa;oDACb,aAAa;oDACb,cAAc;oDACd,SAAQ;8DAEP,sBAAsB,GAAG,CAAC,CAAC,OAAO,sBACjC,6LAAC,+JAAI;4DAAuB,MAAM,MAAM,KAAK;2DAAlC,AAAC,QAAa,OAAN;;;;;;;;;;8DAGvB,6LAAC,qKAAO;oDACN,SAAS;4DAAC,EAAE,MAAM,EAAE,OAAO,EAAE;wDAC3B,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;4DACvC,qBACE,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAU;;wEACV,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI;wEAAC;wEAAG,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;wDAIpD;wDACA,OAAO;oDACT;;;;;;;;;;;;;;;;;;;;;;8CAMR,6LAAC;oCAAI,WAAU;8CACZ,sBAAsB,GAAG,CAAC,CAAC,MAAM,sBAChC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,KAAK,KAAK;4DAAC;;;;;;sEAEvC,6LAAC;4DAAK,WAAU;sEACb,KAAK,IAAI;;;;;;;;;;;;8DAGd,6LAAC;oDAAK,WAAU;8DAAuB,KAAK,KAAK;;;;;;;2CAVzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmB1B;KArHgB", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/recent-appointments.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport {\n  Calendar,\n  Clock,\n  Phone,\n  User,\n  MoreHorizontal,\n  CheckCircle,\n  XCircle,\n  Eye,\n} from 'lucide-react'\nimport { getStatusColor, formatTime } from '@/lib/utils'\nimport { type Appointment } from '@/lib/supabase'\n\ninterface RecentAppointmentsProps {\n  appointments: Appointment[]\n  loading?: boolean\n}\n\nconst getStatusIcon = (status: string) => {\n  switch (status) {\n    case 'confirmed':\n      return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n    case 'pending':\n      return <Clock className=\"h-4 w-4 text-yellow-600\" />\n    case 'completed':\n      return <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n    case 'cancelled':\n      return <XCircle className=\"h-4 w-4 text-red-600\" />\n    case 'in_progress':\n      return <Clock className=\"h-4 w-4 text-purple-600\" />\n    default:\n      return <Clock className=\"h-4 w-4 text-gray-600\" />\n  }\n}\n\nexport function RecentAppointments({ appointments, loading = false }: RecentAppointmentsProps) {\n  return (\n    <Card className=\"border-0 shadow-sm bg-white dark:bg-gray-900\">\n      <CardHeader className=\"pb-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n              Recent Appointments\n            </CardTitle>\n            <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n              Latest scheduled appointments\n            </CardDescription>\n          </div>\n          <Button variant=\"outline\" size=\"sm\">\n            <Calendar className=\"mr-2 h-4 w-4\" />\n            View All\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {loading ? (\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"flex items-center justify-between rounded-lg border p-4\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\" />\n                  <div className=\"space-y-2\">\n                    <div className=\"h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                    <div className=\"h-3 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                  <div className=\"h-3 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : appointments.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n            <Calendar className=\"h-12 w-12 text-muted-foreground/50\" />\n            <h3 className=\"mt-4 text-lg font-semibold\">No appointments</h3>\n            <p className=\"text-muted-foreground\">\n              No recent appointments to display.\n            </p>\n            <Button className=\"mt-4\">\n              <Calendar className=\"mr-2 h-4 w-4\" />\n              Schedule Appointment\n            </Button>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {appointments.slice(0, 5).map((appointment) => (\n              <div\n                key={appointment.id}\n                className=\"flex items-center justify-between rounded-lg border p-4 hover:bg-accent/50 transition-colors\"\n              >\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"flex h-10 w-10 items-center justify-center rounded-full bg-primary/10\">\n                    <User className=\"h-4 w-4 text-primary\" />\n                  </div>\n\n                  <div className=\"space-y-1\">\n                    <div className=\"flex items-center gap-2\">\n                      <p className=\"font-medium\">{appointment.full_name}</p>\n                      <Badge\n                        variant=\"outline\"\n                        className={getStatusColor(appointment.status)}\n                      >\n                        {appointment.status.replace('_', ' ')}\n                      </Badge>\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {appointment.service_type}\n                    </p>\n                    <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-3 w-3\" />\n                        {new Date(appointment.preferred_date).toLocaleDateString('en-IN')}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Clock className=\"h-3 w-3\" />\n                        {formatTime(appointment.preferred_time)}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Phone className=\"h-3 w-3\" />\n                        {appointment.phone}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"flex items-center gap-2\">\n                    {getStatusIcon(appointment.status)}\n                  </div>\n\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button variant=\"ghost\" size=\"icon\">\n                        <MoreHorizontal className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\">\n                      <DropdownMenuItem>\n                        <Eye className=\"mr-2 h-4 w-4\" />\n                        View Details\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <CheckCircle className=\"mr-2 h-4 w-4\" />\n                        Confirm\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Calendar className=\"mr-2 h-4 w-4\" />\n                        Reschedule\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Phone className=\"mr-2 h-4 w-4\" />\n                        Call Patient\n                      </DropdownMenuItem>\n                      <DropdownMenuItem className=\"text-red-600\">\n                        <XCircle className=\"mr-2 h-4 w-4\" />\n                        Cancel\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AArBA;;;;;;;;AA6BA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,6OAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,6LAAC,gNAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,6LAAC,6OAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,6LAAC,0NAAO;gBAAC,WAAU;;;;;;QAC5B,KAAK;YACH,qBAAO,6LAAC,gNAAK;gBAAC,WAAU;;;;;;QAC1B;YACE,qBAAO,6LAAC,gNAAK;gBAAC,WAAU;;;;;;IAC5B;AACF;AAEO,SAAS,mBAAmB,KAA0D;QAA1D,EAAE,YAAY,EAAE,UAAU,KAAK,EAA2B,GAA1D;IACjC,qBACE,6LAAC,2IAAI;QAAC,WAAU;;0BACd,6LAAC,iJAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,gJAAS;oCAAC,WAAU;8CAAyD;;;;;;8CAG9E,6LAAC,sJAAe;oCAAC,WAAU;8CAAmC;;;;;;;;;;;;sCAIhE,6LAAC,+IAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,6LAAC,yNAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAK3C,6LAAC,kJAAW;0BACT,wBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAVT;;;;;;;;;2BAeZ,aAAa,MAAM,KAAK,kBAC1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yNAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,6LAAC,+IAAM;4BAAC,WAAU;;8CAChB,6LAAC,yNAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;yCAKzC,6LAAC;oBAAI,WAAU;8BACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,4BAC7B,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAI;gDAAC,WAAU;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAe,YAAY,SAAS;;;;;;sEACjD,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAW,IAAA,wIAAc,EAAC,YAAY,MAAM;sEAE3C,YAAY,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8DAGrC,6LAAC;oDAAE,WAAU;8DACV,YAAY,YAAY;;;;;;8DAE3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yNAAQ;oEAAC,WAAU;;;;;;gEACnB,IAAI,KAAK,YAAY,cAAc,EAAE,kBAAkB,CAAC;;;;;;;sEAE3D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAK;oEAAC,WAAU;;;;;;gEAChB,IAAA,oIAAU,EAAC,YAAY,cAAc;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAK;oEAAC,WAAU;;;;;;gEAChB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;8CAM1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,cAAc,YAAY,MAAM;;;;;;sDAGnC,6LAAC,+JAAY;;8DACX,6LAAC,sKAAmB;oDAAC,OAAO;8DAC1B,cAAA,6LAAC,+IAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6LAAC,qOAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,6LAAC,sKAAmB;oDAAC,OAAM;;sEACzB,6LAAC,mKAAgB;;8EACf,6LAAC,0MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,6LAAC,mKAAgB;;8EACf,6LAAC,6OAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAG1C,6LAAC,mKAAgB;;8EACf,6LAAC,yNAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,6LAAC,mKAAgB;;8EACf,6LAAC,gNAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,6LAAC,mKAAgB;4DAAC,WAAU;;8EAC1B,6LAAC,0NAAO;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnEvC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;AAgFnC;KAtIgB", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/quick-actions.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  Calendar, \n  Users, \n  MessageSquare, \n  FileText,\n  UserPlus,\n  Bell,\n  Settings,\n  BarChart3,\n  Plus,\n  Search\n} from 'lucide-react'\n\nconst quickActions = [\n  {\n    title: 'New Appointment',\n    description: 'Schedule a new appointment',\n    icon: Calendar,\n    color: 'bg-blue-500 hover:bg-blue-600',\n    action: () => console.log('New appointment')\n  },\n  {\n    title: 'Add Patient',\n    description: 'Register new patient',\n    icon: UserPlus,\n    color: 'bg-green-500 hover:bg-green-600',\n    action: () => console.log('Add patient')\n  },\n  {\n    title: 'Send Message',\n    description: 'Contact patients',\n    icon: MessageSquare,\n    color: 'bg-purple-500 hover:bg-purple-600',\n    action: () => console.log('Send message')\n  },\n  {\n    title: 'Generate Report',\n    description: 'Create analytics report',\n    icon: BarChart3,\n    color: 'bg-orange-500 hover:bg-orange-600',\n    action: () => console.log('Generate report')\n  },\n]\n\nconst recentActions = [\n  {\n    title: 'Patient Search',\n    description: 'Find patient records quickly',\n    icon: Search,\n    href: '/patients'\n  },\n  {\n    title: 'Notifications',\n    description: 'View all notifications',\n    icon: Bell,\n    href: '/communications/notifications'\n  },\n  {\n    title: 'Reports',\n    description: 'Access detailed reports',\n    icon: FileText,\n    href: '/reports'\n  },\n  {\n    title: 'Settings',\n    description: 'Configure system settings',\n    icon: Settings,\n    href: '/settings'\n  },\n]\n\nexport function QuickActions() {\n  return (\n    <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n      <CardHeader className=\"pb-3\">\n        <CardTitle className=\"text-lg text-gray-900 dark:text-white\">Quick Actions</CardTitle>\n        <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n          Frequently used actions and shortcuts\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Primary Actions */}\n        <div className=\"grid grid-cols-2 gap-3\">\n          {quickActions.map((action, index) => {\n            const Icon = action.icon\n            return (\n              <Button\n                key={index}\n                variant=\"outline\"\n                className=\"h-auto flex-col gap-2 p-4 hover:shadow-md transition-all border-gray-200 dark:border-[#404040] hover:bg-gray-50 dark:hover:bg-[#2a2a2a] group\"\n                onClick={action.action}\n              >\n                <div className={`rounded-lg p-2 text-white ${action.color} group-hover:scale-110 transition-transform duration-200`}>\n                  <Icon className=\"h-4 w-4\" />\n                </div>\n                <div className=\"text-center\">\n                  <p className=\"text-xs font-medium text-gray-900 dark:text-white\">{action.title}</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {action.description}\n                  </p>\n                </div>\n              </Button>\n            )\n          })}\n        </div>\n\n        {/* Secondary Actions */}\n        <div className=\"space-y-2 border-t pt-4\">\n          <h4 className=\"text-sm font-medium text-muted-foreground\">\n            Quick Links\n          </h4>\n          {recentActions.map((action, index) => {\n            const Icon = action.icon\n            return (\n              <Button\n                key={index}\n                variant=\"ghost\"\n                className=\"w-full justify-start gap-3 h-auto p-3\"\n                asChild\n              >\n                <a href={action.href}>\n                  <Icon className=\"h-4 w-4 text-muted-foreground\" />\n                  <div className=\"text-left\">\n                    <p className=\"text-sm font-medium\">{action.title}</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {action.description}\n                    </p>\n                  </div>\n                </a>\n              </Button>\n            )\n          })}\n        </div>\n\n        {/* Emergency Actions */}\n        <div className=\"space-y-2 border-t pt-4\">\n          <h4 className=\"text-sm font-medium text-muted-foreground\">\n            Emergency\n          </h4>\n          <Button \n            variant=\"destructive\" \n            size=\"sm\" \n            className=\"w-full\"\n          >\n            <Plus className=\"mr-2 h-3 w-3\" />\n            Emergency Appointment\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAiBA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,aAAa;QACb,MAAM,yNAAQ;QACd,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,6NAAQ;QACd,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,4OAAa;QACnB,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,kOAAS;QACf,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;CACD;AAED,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,aAAa;QACb,MAAM,mNAAM;QACZ,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,6MAAI;QACV,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,6NAAQ;QACd,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,yNAAQ;QACd,MAAM;IACR;CACD;AAEM,SAAS;IACd,qBACE,6LAAC,2IAAI;QAAC,WAAU;;0BACd,6LAAC,iJAAU;gBAAC,WAAU;;kCACpB,6LAAC,gJAAS;wBAAC,WAAU;kCAAwC;;;;;;kCAC7D,6LAAC,sJAAe;wBAAC,WAAU;kCAAmC;;;;;;;;;;;;0BAIhE,6LAAC,kJAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,QAAQ;4BACzB,MAAM,OAAO,OAAO,IAAI;4BACxB,qBACE,6LAAC,+IAAM;gCAEL,SAAQ;gCACR,WAAU;gCACV,SAAS,OAAO,MAAM;;kDAEtB,6LAAC;wCAAI,WAAW,AAAC,6BAAyC,OAAb,OAAO,KAAK,EAAC;kDACxD,cAAA,6LAAC;4CAAK,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqD,OAAO,KAAK;;;;;;0DAC9E,6LAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;;;;;;;;+BAXlB;;;;;wBAgBX;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;4BAGzD,cAAc,GAAG,CAAC,CAAC,QAAQ;gCAC1B,MAAM,OAAO,OAAO,IAAI;gCACxB,qBACE,6LAAC,+IAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,6LAAC;wCAAE,MAAM,OAAO,IAAI;;0DAClB,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuB,OAAO,KAAK;;;;;;kEAChD,6LAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCAVpB;;;;;4BAgBX;;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,6LAAC,+IAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,6LAAC,6MAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;KAhFgB", "debugId": null}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/api-client.ts"], "sourcesContent": ["import { type Appointment, type Patient, type Service } from './supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\nclass ApiClient {\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    const response = await fetch(`/api${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    })\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  // Dashboard\n  async getDashboardStats(): Promise<DashboardStats> {\n    return this.request<DashboardStats>('/dashboard/stats')\n  }\n\n  // Appointments\n  async getAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments')\n  }\n\n  async getAppointment(id: string): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`)\n  }\n\n  async createAppointment(appointment: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(appointment),\n    })\n  }\n\n  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async getPendingAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments/pending')\n  }\n\n  async updatePendingAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/pending?id=${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Patients\n  async getPatients(): Promise<Patient[]> {\n    return this.request<Patient[]>('/patients')\n  }\n\n  async getPatient(id: string): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`)\n  }\n\n  async getPatientAppointments(patientId: string): Promise<any[]> {\n    return this.request<any[]>(`/patients/${patientId}/appointments`)\n  }\n\n  async createPatient(patient: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(patient),\n    })\n  }\n\n  async updatePatient(id: string, updates: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Services\n  async getServices(): Promise<Service[]> {\n    return this.request<Service[]>('/services')\n  }\n\n  async getService(id: string): Promise<Service> {\n    return this.request<Service>(`/services/${id}`)\n  }\n\n  async createService(service: Partial<Service>): Promise<Service> {\n    return this.request<Service>('/services', {\n      method: 'POST',\n      body: JSON.stringify(service),\n    })\n  }\n\n  async updateService(id: string, updates: Partial<Service>): Promise<Service> {\n    return this.request<Service>(`/services/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Reports\n  async getReportsData(dateRange?: string): Promise<any> {\n    const params = dateRange ? `?dateRange=${dateRange}` : ''\n    return this.request<any>(`/reports${params}`)\n  }\n\n  // Medical Records\n  async getMedicalRecords(patientId?: string): Promise<any[]> {\n    const params = patientId ? `?patientId=${patientId}` : ''\n    return this.request<any[]>(`/medical-records${params}`)\n  }\n\n  async getMedicalRecord(id: string): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`)\n  }\n\n  async createMedicalRecord(record: any): Promise<any> {\n    return this.request<any>('/medical-records', {\n      method: 'POST',\n      body: JSON.stringify(record),\n    })\n  }\n\n  async updateMedicalRecord(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Availability Slots\n  async getAvailabilitySlots(): Promise<any[]> {\n    return this.request<any[]>('/availability-slots')\n  }\n\n  async getAvailabilitySlot(id: string): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`)\n  }\n\n  async createAvailabilitySlot(slot: any): Promise<any> {\n    return this.request<any>('/availability-slots', {\n      method: 'POST',\n      body: JSON.stringify(slot),\n    })\n  }\n\n  async updateAvailabilitySlot(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async deleteAvailabilitySlot(id: string): Promise<void> {\n    return this.request<void>(`/availability-slots/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async checkSlotAvailability(date: string, time?: string): Promise<any> {\n    const params = time ? `?date=${date}&time=${time}` : `?date=${date}`\n    return this.request<any>(`/availability-slots/check${params}`)\n  }\n}\n\nexport const api = new ApiClient()\n"], "names": [], "mappings": ";;;;AAaA,MAAM;IACJ,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,MAAM,WAAW,MAAM,MAAM,AAAC,OAAe,OAAT,WAAY;YAC9C,SAAS;gBACP,gBAAgB;mBACb,oBAAA,8BAAA,QAAS,OAAO,AAAnB;YACF;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAA0C,OAApB,SAAS,UAAU;QAC5D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,oBAA6C;QACjD,OAAO,IAAI,CAAC,OAAO,CAAiB;IACtC;IAEA,eAAe;IACf,MAAM,kBAA0C;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,eAAe,EAAU,EAAwB;QACrD,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,iBAAmB,OAAH;IACpD;IAEA,MAAM,kBAAkB,WAAiC,EAAwB;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAc,iBAAiB;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,OAA6B,EAAwB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,iBAAmB,OAAH,KAAM;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAyB,EAAU,EAAE,OAA6B,EAAwB;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,4BAA8B,OAAH,KAAM;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH;IAC5C;IAEA,MAAM,uBAAuB,SAAiB,EAAkB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAQ,AAAC,aAAsB,OAAV,WAAU;IACpD;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH;IAC5C;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,UAAU;IACV,MAAM,eAAe,SAAkB,EAAgB;QACrD,MAAM,SAAS,YAAY,AAAC,cAAuB,OAAV,aAAc;QACvD,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,WAAiB,OAAP;IACtC;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,SAAkB,EAAkB;QAC1D,MAAM,SAAS,YAAY,AAAC,cAAuB,OAAV,aAAc;QACvD,OAAO,IAAI,CAAC,OAAO,CAAQ,AAAC,mBAAyB,OAAP;IAChD;IAEA,MAAM,iBAAiB,EAAU,EAAgB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,oBAAsB,OAAH;IAC/C;IAEA,MAAM,oBAAoB,MAAW,EAAgB;QACnD,OAAO,IAAI,CAAC,OAAO,CAAM,oBAAoB;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,oBAAoB,EAAU,EAAE,OAAY,EAAgB;QAChE,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,oBAAsB,OAAH,KAAM;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,uBAAuC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,MAAM,oBAAoB,EAAU,EAAgB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,uBAAyB,OAAH;IAClD;IAEA,MAAM,uBAAuB,IAAS,EAAgB;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,uBAAuB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAE,OAAY,EAAgB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,uBAAyB,OAAH,KAAM;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAiB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAO,AAAC,uBAAyB,OAAH,KAAM;YACrD,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,IAAY,EAAE,IAAa,EAAgB;QACrE,MAAM,SAAS,OAAO,AAAC,SAAqB,OAAb,MAAK,UAAa,OAAL,QAAS,AAAC,SAAa,OAAL;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,4BAAkC,OAAP;IACvD;AACF;AAEO,MAAM,MAAM,IAAI", "debugId": null}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/dashboard-overview.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Calendar } from 'lucide-react'\nimport { formatDate } from '@/lib/utils'\nimport { StatsCards } from './stats-cards'\nimport { RevenueChart } from './revenue-chart'\nimport { AppointmentChart } from './appointment-chart'\nimport { RecentAppointments } from './recent-appointments'\nimport { QuickActions } from './quick-actions'\n\n// Client-side component to handle date rendering and avoid hydration mismatch\nfunction TodayDate() {\n  const [currentDate, setCurrentDate] = useState<string>('')\n\n  useEffect(() => {\n    setCurrentDate(formatDate(new Date()))\n  }, [])\n\n  return <span>{currentDate}</span>\n}\nimport { api } from '@/lib/api-client'\nimport { type Appointment } from '@/lib/supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\n\n\nexport function DashboardOverview() {\n  const [currentTime, setCurrentTime] = useState(new Date())\n  const [stats, setStats] = useState<DashboardStats | null>(null)\n  const [appointments, setAppointments] = useState<Appointment[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date())\n    }, 1000)\n\n    return () => clearInterval(timer)\n  }, [])\n\n  useEffect(() => {\n    async function loadDashboardData() {\n      try {\n        setLoading(true)\n        const [statsData, appointmentsData] = await Promise.all([\n          api.getDashboardStats(),\n          api.getAppointments()\n        ])\n\n        setStats(statsData)\n        // Get recent appointments (last 5)\n        setAppointments(appointmentsData.slice(0, 5))\n      } catch (error) {\n        console.error('Error loading dashboard data:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadDashboardData()\n  }, [])\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 dark:text-white\">Dashboard</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n            Welcome back! Here&apos;s what&apos;s happening at your dental practice today.\n          </p>\n        </div>\n        <div className=\"flex items-center gap-4\">\n          <div className=\"text-right bg-white dark:bg-[#1a1a1a] rounded-xl p-4 border border-gray-200 dark:border-[#404040] shadow-sm\">\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">Current Time</p>\n            <p className=\"text-xl font-bold text-gray-900 dark:text-white\">\n              {currentTime.toLocaleTimeString('en-IN', {\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n              })}\n            </p>\n          </div>\n          <Button className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 shadow-lg px-6 py-3 h-auto\">\n            <Calendar className=\"mr-2 h-5 w-5\" />\n            New Appointment\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <StatsCards stats={stats} loading={loading} />\n\n      {/* Charts Row */}\n      <div className=\"grid gap-6 md:grid-cols-2\">\n        <RevenueChart />\n        <AppointmentChart />\n      </div>\n\n      {/* Main Content Grid */}\n      <div className=\"grid gap-6 md:grid-cols-3\">\n        {/* Recent Appointments */}\n        <div className=\"md:col-span-2\">\n          <RecentAppointments appointments={appointments} loading={loading} />\n        </div>\n\n        {/* Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Quick Actions */}\n          <QuickActions />\n\n          {/* Alerts & Notifications */}\n          <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg text-gray-900 dark:text-white\">Alerts & Notifications</CardTitle>\n              <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n                Important updates and reminders\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex items-center justify-center py-8 text-muted-foreground\">\n                <p>No alerts at this time</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                View All Notifications\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* Today's Schedule */}\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg\">Today&apos;s Schedule</CardTitle>\n              <CardDescription>\n                <TodayDate />\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">9:00 AM</span>\n                  <span>Priya Sharma - Checkup</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">11:30 AM</span>\n                  <span>Rajesh Kumar - Root Canal</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">2:00 PM</span>\n                  <span>Anita Patel - Whitening</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">4:30 PM</span>\n                  <span>Vikram Singh - Implants</span>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                <Calendar className=\"mr-2 h-3 w-3\" />\n                View Full Calendar\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAYA;;;AAvBA;;;;;;;;;;;AAaA,8EAA8E;AAC9E,SAAS;;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAS;IAEvD,IAAA,0KAAS;+BAAC;YACR,eAAe,IAAA,oIAAU,EAAC,IAAI;QAChC;8BAAG,EAAE;IAEL,qBAAO,6LAAC;kBAAM;;;;;;AAChB;GARS;KAAA;;AAyBF,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC,IAAI;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAwB;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAgB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;uCAAC;YACR,MAAM,QAAQ;qDAAY;oBACxB,eAAe,IAAI;gBACrB;oDAAG;YAEH;+CAAO,IAAM,cAAc;;QAC7B;sCAAG,EAAE;IAEL,IAAA,0KAAS;uCAAC;YACR,eAAe;gBACb,IAAI;oBACF,WAAW;oBACX,MAAM,CAAC,WAAW,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBACtD,qIAAG,CAAC,iBAAiB;wBACrB,qIAAG,CAAC,eAAe;qBACpB;oBAED,SAAS;oBACT,mCAAmC;oBACnC,gBAAgB,iBAAiB,KAAK,CAAC,GAAG;gBAC5C,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;gBACjD,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;sCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAChF,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;kDACV,YAAY,kBAAkB,CAAC,SAAS;4CACvC,MAAM;4CACN,QAAQ;4CACR,QAAQ;wCACV;;;;;;;;;;;;0CAGJ,6LAAC,+IAAM;gCAAC,WAAU;;kDAChB,6LAAC,yNAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,kKAAU;gBAAC,OAAO;gBAAO,SAAS;;;;;;0BAGnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,sKAAY;;;;;kCACb,6LAAC,8KAAgB;;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,kLAAkB;4BAAC,cAAc;4BAAc,SAAS;;;;;;;;;;;kCAI3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,sKAAY;;;;;0CAGb,6LAAC,2IAAI;gCAAC,WAAU;;kDACd,6LAAC,iJAAU;wCAAC,WAAU;;0DACpB,6LAAC,gJAAS;gDAAC,WAAU;0DAAwC;;;;;;0DAC7D,6LAAC,sJAAe;gDAAC,WAAU;0DAAmC;;;;;;;;;;;;kDAIhE,6LAAC,kJAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAE;;;;;;;;;;;0DAEL,6LAAC,+IAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;0CAO3D,6LAAC,2IAAI;;kDACH,6LAAC,iJAAU;wCAAC,WAAU;;0DACpB,6LAAC,gJAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,6LAAC,sJAAe;0DACd,cAAA,6LAAC;;;;;;;;;;;;;;;;kDAGL,6LAAC,kJAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,6LAAC,+IAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,6LAAC,yNAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;IA5IgB;MAAA", "debugId": null}}]}