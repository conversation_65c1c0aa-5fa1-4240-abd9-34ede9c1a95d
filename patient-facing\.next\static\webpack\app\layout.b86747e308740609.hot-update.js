"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"56505e1a3df1\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGV0cmFcXE9uZURyaXZlXFxEZXNrdG9wXFxEZW50aXN0IHdlYnNpdGVcXHBhdGllbnQtZmFjaW5nXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTY1MDVlMWEzZGYxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/availability-checker.tsx":
/*!*********************************************!*\
  !*** ./components/availability-checker.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AvailabilityChecker: () => (/* binding */ AvailabilityChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Loader2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ AvailabilityChecker auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AvailabilityChecker(param) {\n    let { selectedDate, onTimeSelect, selectedTime } = param;\n    _s();\n    const [availableSlots, setAvailableSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AvailabilityChecker.useEffect\": ()=>{\n            if (selectedDate) {\n                fetchAvailability();\n            }\n        }\n    }[\"AvailabilityChecker.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAvailability = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/availability?date=\".concat(selectedDate));\n            const data = await response.json();\n            if (data.available && data.timeSlots) {\n                // Filter out fully booked slots and format the data\n                const availableTimeSlots = data.timeSlots.filter((slot)=>{\n                    const currentBookings = slot.current_appointments || 0;\n                    return currentBookings < slot.max_appointments;\n                }).map((slot)=>({\n                        time: slot.time,\n                        available: true,\n                        capacity: slot.max_appointments - (slot.current_appointments || 0),\n                        total: slot.max_appointments\n                    }));\n                setAvailableSlots(availableTimeSlots);\n            } else {\n                setAvailableSlots([]);\n                setError(data.message || 'No availability for this date');\n            }\n        } catch (err) {\n            setError('Failed to check availability');\n            console.error('Error fetching availability:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTimeSelect = (time)=>{\n        onTimeSelect(time);\n    };\n    if (!selectedDate) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"Please select a date to view available time slots\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-primary mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"Checking availability...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: fetchAvailability,\n                    variant: \"outline\",\n                    size: \"sm\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    if (availableSlots.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-12 w-12 text-orange-500 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-orange-600 mb-2\",\n                    children: [\n                        \"No available slots for \",\n                        (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(new Date(selectedDate), 'MMMM d, yyyy')\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground text-sm\",\n                    children: \"Please select a different date or contact us directly\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-5 w-5 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium text-foreground\",\n                        children: [\n                            \"Available slots for \",\n                            (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(new Date(selectedDate), 'MMMM d, yyyy')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-3\",\n                children: availableSlots.map((slot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: selectedTime === slot.time ? \"default\" : \"outline\",\n                        className: \"p-3 h-auto flex flex-col items-center transition-all duration-300 \".concat(selectedTime === slot.time ? 'bg-primary text-primary-foreground shadow-lg' : 'hover:bg-primary/10'),\n                        onClick: ()=>handleTimeSelect(slot.time),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: slot.time\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3 w-3 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            slot.capacity || (slot.maxAppointments && slot.currentAppointments ? slot.maxAppointments - slot.currentAppointments : 'Available'),\n                                            slot.capacity ? ' left' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, slot.time, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-muted/50 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Loader2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4 inline text-green-500 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        \"All times shown are available for booking. Slots are updated in real-time.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\availability-checker.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(AvailabilityChecker, \"PAVtCGIR4nFoNKi7zyMA/FVgtIU=\");\n_c = AvailabilityChecker;\nvar _c;\n$RefreshReg$(_c, \"AvailabilityChecker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/availability-checker.tsx\n"));

/***/ })

});