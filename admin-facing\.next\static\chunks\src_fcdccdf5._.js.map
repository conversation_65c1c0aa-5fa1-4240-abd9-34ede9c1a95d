{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,2KAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,2KAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,2KAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,2KAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,IAAA,4HAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,2KAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/api-client.ts"], "sourcesContent": ["import { type Appointment, type Patient, type Service } from './supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\nclass ApiClient {\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    const response = await fetch(`/api${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    })\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  // Dashboard\n  async getDashboardStats(): Promise<DashboardStats> {\n    return this.request<DashboardStats>('/dashboard/stats')\n  }\n\n  // Appointments\n  async getAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments')\n  }\n\n  async getAppointment(id: string): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`)\n  }\n\n  async createAppointment(appointment: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(appointment),\n    })\n  }\n\n  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async getPendingAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments/pending')\n  }\n\n  async updatePendingAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/pending?id=${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Patients\n  async getPatients(): Promise<Patient[]> {\n    return this.request<Patient[]>('/patients')\n  }\n\n  async getPatient(id: string): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`)\n  }\n\n  async getPatientAppointments(patientId: string): Promise<any[]> {\n    return this.request<any[]>(`/patients/${patientId}/appointments`)\n  }\n\n  async createPatient(patient: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(patient),\n    })\n  }\n\n  async updatePatient(id: string, updates: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Services\n  async getServices(): Promise<Service[]> {\n    return this.request<Service[]>('/services')\n  }\n\n  async getService(id: string): Promise<Service> {\n    return this.request<Service>(`/services/${id}`)\n  }\n\n  async createService(service: Partial<Service>): Promise<Service> {\n    return this.request<Service>('/services', {\n      method: 'POST',\n      body: JSON.stringify(service),\n    })\n  }\n\n  async updateService(id: string, updates: Partial<Service>): Promise<Service> {\n    return this.request<Service>(`/services/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Reports\n  async getReportsData(dateRange?: string): Promise<any> {\n    const params = dateRange ? `?dateRange=${dateRange}` : ''\n    return this.request<any>(`/reports${params}`)\n  }\n\n  // Medical Records\n  async getMedicalRecords(patientId?: string): Promise<any[]> {\n    const params = patientId ? `?patientId=${patientId}` : ''\n    return this.request<any[]>(`/medical-records${params}`)\n  }\n\n  async getMedicalRecord(id: string): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`)\n  }\n\n  async createMedicalRecord(record: any): Promise<any> {\n    return this.request<any>('/medical-records', {\n      method: 'POST',\n      body: JSON.stringify(record),\n    })\n  }\n\n  async updateMedicalRecord(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Availability Slots\n  async getAvailabilitySlots(): Promise<any[]> {\n    return this.request<any[]>('/availability-slots')\n  }\n\n  async getAvailabilitySlot(id: string): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`)\n  }\n\n  async createAvailabilitySlot(slot: any): Promise<any> {\n    return this.request<any>('/availability-slots', {\n      method: 'POST',\n      body: JSON.stringify(slot),\n    })\n  }\n\n  async updateAvailabilitySlot(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async deleteAvailabilitySlot(id: string): Promise<void> {\n    return this.request<void>(`/availability-slots/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async checkSlotAvailability(date: string, time?: string): Promise<any> {\n    const params = time ? `?date=${date}&time=${time}` : `?date=${date}`\n    return this.request<any>(`/availability-slots/check${params}`)\n  }\n}\n\nexport const api = new ApiClient()\n"], "names": [], "mappings": ";;;;AAaA,MAAM;IACJ,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,MAAM,WAAW,MAAM,MAAM,AAAC,OAAe,OAAT,WAAY;YAC9C,SAAS;gBACP,gBAAgB;mBACb,oBAAA,8BAAA,QAAS,OAAO,AAAnB;YACF;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAA0C,OAApB,SAAS,UAAU;QAC5D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,oBAA6C;QACjD,OAAO,IAAI,CAAC,OAAO,CAAiB;IACtC;IAEA,eAAe;IACf,MAAM,kBAA0C;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,eAAe,EAAU,EAAwB;QACrD,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,iBAAmB,OAAH;IACpD;IAEA,MAAM,kBAAkB,WAAiC,EAAwB;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAc,iBAAiB;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,OAA6B,EAAwB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,iBAAmB,OAAH,KAAM;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAyB,EAAU,EAAE,OAA6B,EAAwB;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAc,AAAC,4BAA8B,OAAH,KAAM;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH;IAC5C;IAEA,MAAM,uBAAuB,SAAiB,EAAkB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAQ,AAAC,aAAsB,OAAV,WAAU;IACpD;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH;IAC5C;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,UAAU;IACV,MAAM,eAAe,SAAkB,EAAgB;QACrD,MAAM,SAAS,YAAY,AAAC,cAAuB,OAAV,aAAc;QACvD,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,WAAiB,OAAP;IACtC;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,SAAkB,EAAkB;QAC1D,MAAM,SAAS,YAAY,AAAC,cAAuB,OAAV,aAAc;QACvD,OAAO,IAAI,CAAC,OAAO,CAAQ,AAAC,mBAAyB,OAAP;IAChD;IAEA,MAAM,iBAAiB,EAAU,EAAgB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,oBAAsB,OAAH;IAC/C;IAEA,MAAM,oBAAoB,MAAW,EAAgB;QACnD,OAAO,IAAI,CAAC,OAAO,CAAM,oBAAoB;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,oBAAoB,EAAU,EAAE,OAAY,EAAgB;QAChE,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,oBAAsB,OAAH,KAAM;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,uBAAuC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,MAAM,oBAAoB,EAAU,EAAgB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,uBAAyB,OAAH;IAClD;IAEA,MAAM,uBAAuB,IAAS,EAAgB;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,uBAAuB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAE,OAAY,EAAgB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,uBAAyB,OAAH,KAAM;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAiB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAO,AAAC,uBAAyB,OAAH,KAAM;YACrD,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,IAAY,EAAE,IAAa,EAAgB;QACrE,MAAM,SAAS,OAAO,AAAC,SAAqB,OAAb,MAAK,UAAa,OAAL,QAAS,AAAC,SAAa,OAAL;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAM,AAAC,4BAAkC,OAAP;IACvD;AACF;AAEO,MAAM,MAAM,IAAI", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,6KAAoB;AAEnC,MAAM,gBAAgB,gLAAuB;AAE7C,MAAM,eAAe,+KAAsB;AAE3C,MAAM,cAAc,8KAAqB;AAEzC,MAAM,8BAAgB,2KAAgB,CAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,gLAAuB;QACtB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,2JACA;QAED,GAAG,KAAK;;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,gLAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,2KAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,gLAAuB;gBACtB,KAAK;gBACL,WAAW,IAAA,4HAAE,EACX,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,8KAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,oMAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,gLAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,IAAA,4HAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,IAAA,4HAAE,EACX,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,2KAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAqB;QACpB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,8KAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,2KAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oLAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,oLAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/appointments/appointment-details-modal.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport {\r\n  <PERSON>alog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Separator } from '@/components/ui/separator'\r\nimport {\r\n  User,\r\n  Phone,\r\n  Mail,\r\n  Calendar,\r\n  Clock,\r\n  Activity,\r\n  FileText,\r\n  CreditCard,\r\n  AlertCircle,\r\n  CheckCircle,\r\n  XCircle,\r\n  Timer\r\n} from 'lucide-react'\r\nimport { type Appointment } from '@/lib/supabase'\r\n\r\ninterface AppointmentDetailsModalProps {\r\n  appointment: Appointment | null\r\n  open: boolean\r\n  onOpenChange: (open: boolean) => void\r\n}\r\n\r\nexport function AppointmentDetailsModal({\r\n  appointment,\r\n  open,\r\n  onOpenChange\r\n}: AppointmentDetailsModalProps) {\r\n  if (!appointment) return null\r\n\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case 'confirmed':\r\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n      case 'completed':\r\n        return <CheckCircle className=\"h-4 w-4 text-blue-600\" />\r\n      case 'cancelled':\r\n        return <XCircle className=\"h-4 w-4 text-red-600\" />\r\n      case 'pending':\r\n        return <Timer className=\"h-4 w-4 text-yellow-600\" />\r\n      default:\r\n        return <AlertCircle className=\"h-4 w-4 text-gray-600\" />\r\n    }\r\n  }\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'confirmed':\r\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\r\n      case 'completed':\r\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\r\n      case 'cancelled':\r\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\r\n      case 'pending':\r\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\r\n    }\r\n  }\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'urgent':\r\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\r\n      case 'high':\r\n        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'\r\n      case 'normal':\r\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\r\n      case 'low':\r\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\r\n    }\r\n  }\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      weekday: 'long',\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    })\r\n  }\r\n\r\n  const formatTime = (timeString: string) => {\r\n    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {\r\n      hour: 'numeric',\r\n      minute: '2-digit',\r\n      hour12: true\r\n    })\r\n  }\r\n\r\n  const formatCurrency = (amount: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(amount / 100)\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader className=\"space-y-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <DialogTitle className=\"text-2xl font-bold text-foreground\">\r\n              Appointment Details\r\n            </DialogTitle>\r\n            <div className=\"flex items-center space-x-2\">\r\n              {getStatusIcon(appointment.status)}\r\n              <Badge className={getStatusColor(appointment.status)}>\r\n                {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}\r\n              </Badge>\r\n            </div>\r\n          </div>\r\n          <DialogDescription className=\"text-muted-foreground\">\r\n            Complete information for appointment #{appointment.id.slice(-8)}\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\">\r\n          {/* Patient Information */}\r\n          <Card className=\"shadow-sm border-border\">\r\n            <CardHeader className=\"pb-3\">\r\n              <CardTitle className=\"flex items-center space-x-2 text-lg\">\r\n                <User className=\"h-5 w-5 text-primary\" />\r\n                <span>Patient Information</span>\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <User className=\"h-4 w-4 text-muted-foreground\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-muted-foreground\">Full Name</p>\r\n                    <p className=\"font-semibold text-foreground\">{appointment.name}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Mail className=\"h-4 w-4 text-muted-foreground\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-muted-foreground\">Email</p>\r\n                    <p className=\"text-foreground\">{appointment.email}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Phone className=\"h-4 w-4 text-muted-foreground\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-muted-foreground\">Phone</p>\r\n                    <p className=\"text-foreground\">{appointment.phone}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Appointment Details */}\r\n          <Card className=\"shadow-sm border-border\">\r\n            <CardHeader className=\"pb-3\">\r\n              <CardTitle className=\"flex items-center space-x-2 text-lg\">\r\n                <Calendar className=\"h-5 w-5 text-primary\" />\r\n                <span>Appointment Details</span>\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Activity className=\"h-4 w-4 text-muted-foreground\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-muted-foreground\">Service</p>\r\n                    <p className=\"font-semibold text-foreground\">{appointment.service}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Calendar className=\"h-4 w-4 text-muted-foreground\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-muted-foreground\">Date</p>\r\n                    <p className=\"text-foreground\">\r\n                      {formatDate(appointment.appointment_date || appointment.preferred_date)}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Clock className=\"h-4 w-4 text-muted-foreground\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-muted-foreground\">Time</p>\r\n                    <p className=\"text-foreground\">{formatTime(appointment.preferred_time)}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Timer className=\"h-4 w-4 text-muted-foreground\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-muted-foreground\">Duration</p>\r\n                    <p className=\"text-foreground\">{appointment.duration || 60} minutes</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAOA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;AAkCO,SAAS,wBAAwB,KAIT;QAJS,EACtC,WAAW,EACX,IAAI,EACJ,YAAY,EACiB,GAJS;IAKtC,IAAI,CAAC,aAAa,OAAO;IAEzB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,6OAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,0NAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,gNAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,sOAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,AAAC,cAAwB,OAAX,aAAc,kBAAkB,CAAC,SAAS;YACtE,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC,SAAS;IACrB;IAEA,qBACE,6LAAC,+IAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,sJAAa;YAAC,WAAU;;8BACvB,6LAAC,qJAAY;oBAAC,WAAU;;sCACtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oJAAW;oCAAC,WAAU;8CAAqC;;;;;;8CAG5D,6LAAC;oCAAI,WAAU;;wCACZ,cAAc,YAAY,MAAM;sDACjC,6LAAC,6IAAK;4CAAC,WAAW,eAAe,YAAY,MAAM;sDAChD,YAAY,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,YAAY,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;sCAI7E,6LAAC,0JAAiB;4BAAC,WAAU;;gCAAwB;gCACZ,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;8BAIjE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,2IAAI;4BAAC,WAAU;;8CACd,6LAAC,iJAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC,gJAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC,kJAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAiC,YAAY,IAAI;;;;;;;;;;;;;;;;;;0DAIlE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAmB,YAAY,KAAK;;;;;;;;;;;;;;;;;;0DAIrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gNAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAmB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ3D,6LAAC,2IAAI;4BAAC,WAAU;;8CACd,6LAAC,iJAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC,gJAAS;wCAAC,WAAU;;0DACnB,6LAAC,yNAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC,kJAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yNAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAiC,YAAY,OAAO;;;;;;;;;;;;;;;;;;0DAIrE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yNAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EACV,WAAW,YAAY,gBAAgB,IAAI,YAAY,cAAc;;;;;;;;;;;;;;;;;;0DAK5E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gNAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAmB,WAAW,YAAY,cAAc;;;;;;;;;;;;;;;;;;0DAIzE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gNAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;;oEAAmB,YAAY,QAAQ,IAAI;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/E;KAxLgB", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/page-header.tsx"], "sourcesContent": ["import { ReactNode } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { LucideIcon } from 'lucide-react'\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: LucideIcon\n  actions?: ReactNode\n  children?: ReactNode\n}\n\nexport function PageHeader({ \n  title, \n  description, \n  icon: Icon, \n  actions, \n  children \n}: PageHeaderProps) {\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          {Icon && (\n            <div className=\"p-3 rounded-xl bg-gradient-to-r from-orange-500 to-orange-600 shadow-lg\">\n              <Icon className=\"h-6 w-6 text-white\" />\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 dark:text-white\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n        {actions && (\n          <div className=\"flex items-center gap-3\">\n            {actions}\n          </div>\n        )}\n      </div>\n      \n      {/* Additional content like stats cards */}\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAYO,SAAS,WAAW,KAMT;QANS,EACzB,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,OAAO,EACP,QAAQ,EACQ,GANS;IAOzB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAGpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAKR,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAMN;;;;;;;AAGP;KAvCgB", "debugId": null}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/page-layout.tsx"], "sourcesContent": ["import { ReactNode } from 'react'\nimport { PageHeader } from './page-header'\nimport { LucideIcon } from 'lucide-react'\n\ninterface PageLayoutProps {\n  title: string\n  description?: string\n  icon?: LucideIcon\n  actions?: ReactNode\n  stats?: ReactNode\n  children: ReactNode\n}\n\nexport function PageLayout({ \n  title, \n  description, \n  icon, \n  actions, \n  stats, \n  children \n}: PageLayoutProps) {\n  return (\n    <div className=\"space-y-8\">\n      <PageHeader \n        title={title} \n        description={description} \n        icon={icon} \n        actions={actions}\n      >\n        {stats}\n      </PageHeader>\n      \n      <div className=\"space-y-6\">\n        {children}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAYO,SAAS,WAAW,KAOT;QAPS,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACQ,GAPS;IAQzB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2JAAU;gBACT,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,SAAS;0BAER;;;;;;0BAGH,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;KAxBgB", "debugId": null}}, {"offset": {"line": 1625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/stats-card.tsx"], "sourcesContent": ["import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { LucideIcon, ArrowUpRight, ArrowDownRight } from 'lucide-react'\n\ninterface StatsCardProps {\n  title: string\n  value: string | number\n  description?: string\n  icon: LucideIcon\n  trend?: number\n  loading?: boolean\n  color?: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'yellow'\n}\n\nexport function StatsCard({ \n  title, \n  value, \n  description, \n  icon: Icon, \n  trend, \n  loading = false,\n  color = 'orange'\n}: StatsCardProps) {\n  if (loading) {\n    return (\n      <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-2\">\n              <div className=\"h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n              <div className=\"h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n            </div>\n            <div className=\"h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse\" />\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  const colorClasses = {\n    blue: {\n      accent: 'from-blue-500 to-blue-600',\n      icon: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'\n    },\n    green: {\n      accent: 'from-green-500 to-green-600',\n      icon: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400'\n    },\n    orange: {\n      accent: 'from-orange-500 to-orange-600',\n      icon: 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400'\n    },\n    purple: {\n      accent: 'from-purple-500 to-purple-600',\n      icon: 'bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'\n    },\n    red: {\n      accent: 'from-red-500 to-red-600',\n      icon: 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400'\n    },\n    yellow: {\n      accent: 'from-yellow-500 to-yellow-600',\n      icon: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400'\n    }\n  }\n\n  const isPositiveTrend = trend !== undefined && trend > 0\n  const TrendIcon = isPositiveTrend ? ArrowUpRight : ArrowDownRight\n\n  return (\n    <Card className=\"relative overflow-hidden bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040] hover:shadow-lg transition-all duration-200 group\">\n      {/* Gradient accent */}\n      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${colorClasses[color].accent}`} />\n\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n        <CardTitle className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n          {title}\n        </CardTitle>\n        <div className={`p-2.5 rounded-xl ${colorClasses[color].icon} group-hover:scale-110 transition-transform duration-200`}>\n          <Icon className=\"h-5 w-5\" />\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-3\">\n        <div className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          {value}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          {description && (\n            <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n              {description}\n            </span>\n          )}\n          {trend !== undefined && (\n            <div className=\"flex items-center gap-1\">\n              <TrendIcon className={`h-3 w-3 ${\n                isPositiveTrend\n                  ? 'text-green-600 dark:text-green-400'\n                  : 'text-red-600 dark:text-red-400'\n              }`} />\n              <span className={`text-xs font-medium ${\n                isPositiveTrend\n                  ? 'text-green-600 dark:text-green-400'\n                  : 'text-red-600 dark:text-red-400'\n              }`}>\n                {Math.abs(trend)}%\n              </span>\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\ninterface StatsGridProps {\n  children: React.ReactNode\n  columns?: 2 | 3 | 4\n}\n\nexport function StatsGrid({ children, columns = 4 }: StatsGridProps) {\n  const gridClasses = {\n    2: 'grid gap-6 md:grid-cols-2',\n    3: 'grid gap-6 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid gap-6 md:grid-cols-2 lg:grid-cols-4'\n  }\n\n  return (\n    <div className={gridClasses[columns]}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;;;;AAYO,SAAS,UAAU,KAQT;QARS,EACxB,KAAK,EACL,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,QAAQ,EACD,GARS;IASxB,IAAI,SAAS;QACX,qBACE,6LAAC,2IAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,kJAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,MAAM,eAAe;QACnB,MAAM;YACJ,QAAQ;YACR,MAAM;QACR;QACA,OAAO;YACL,QAAQ;YACR,MAAM;QACR;QACA,QAAQ;YACN,QAAQ;YACR,MAAM;QACR;QACA,QAAQ;YACN,QAAQ;YACR,MAAM;QACR;QACA,KAAK;YACH,QAAQ;YACR,MAAM;QACR;QACA,QAAQ;YACN,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,UAAU,aAAa,QAAQ;IACvD,MAAM,YAAY,kBAAkB,6OAAY,GAAG,mPAAc;IAEjE,qBACE,6LAAC,2IAAI;QAAC,WAAU;;0BAEd,6LAAC;gBAAI,WAAW,AAAC,qDAA+E,OAA3B,YAAY,CAAC,MAAM,CAAC,MAAM;;;;;;0BAE/F,6LAAC,iJAAU;gBAAC,WAAU;;kCACpB,6LAAC,gJAAS;wBAAC,WAAU;kCAClB;;;;;;kCAEH,6LAAC;wBAAI,WAAW,AAAC,oBAA4C,OAAzB,YAAY,CAAC,MAAM,CAAC,IAAI,EAAC;kCAC3D,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;0BAGpB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC;gCAAK,WAAU;0CACb;;;;;;4BAGJ,UAAU,2BACT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAU,WAAW,AAAC,WAItB,OAHC,kBACI,uCACA;;;;;;kDAEN,6LAAC;wCAAK,WAAW,AAAC,uBAIjB,OAHC,kBACI,uCACA;;4CAEH,KAAK,GAAG,CAAC;4CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC;KAlGgB;AAyGT,SAAS,UAAU,KAAyC;QAAzC,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAkB,GAAzC;IACxB,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAW,WAAW,CAAC,QAAQ;kBACjC;;;;;;AAGP;MAZgB", "debugId": null}}, {"offset": {"line": 1860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/appointments/appointments-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  Calendar,\n  Search,\n  Plus,\n  RefreshCw,\n  Clock,\n  User,\n  Phone,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n} from 'lucide-react'\nimport { api } from '@/lib/api-client'\nimport { type Appointment } from '@/lib/supabase'\nimport { AppointmentDetailsModal } from './appointment-details-modal'\nimport { PageLayout } from '@/components/ui/page-layout'\nimport { StatsCard, StatsGrid } from '@/components/ui/stats-card'\n\n// Utility functions\nconst getStatusColor = (status: string) => {\n  switch (status) {\n    case 'confirmed':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n    case 'pending':\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'\n    case 'completed':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n    case 'cancelled':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n    case 'in_progress':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n  }\n}\n\nconst getStatusIcon = (status: string) => {\n  switch (status) {\n    case 'confirmed':\n      return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n    case 'pending':\n      return <Clock className=\"h-4 w-4 text-yellow-600\" />\n    case 'completed':\n      return <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n    case 'cancelled':\n      return <XCircle className=\"h-4 w-4 text-red-600\" />\n    case 'in_progress':\n      return <AlertCircle className=\"h-4 w-4 text-purple-600\" />\n    default:\n      return <Clock className=\"h-4 w-4 text-gray-600\" />\n  }\n}\n\nconst formatDate = (dateString: string) => {\n  return new Date(dateString).toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  })\n}\n\nconst formatTime = (timeString: string) => {\n  return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-IN', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n\ninterface AppointmentStats {\n  total: number\n  pending: number\n  confirmed: number\n  completed: number\n  cancelled: number\n}\n\nexport function AppointmentsPage() {\n  const [appointments, setAppointments] = useState<Appointment[]>([])\n  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([])\n  const [stats, setStats] = useState<AppointmentStats | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [searchQuery, setSearchQuery] = useState('')\n  const [statusFilter, setStatusFilter] = useState('all')\n  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null)\n  const [detailsModalOpen, setDetailsModalOpen] = useState(false)\n\n  useEffect(() => {\n    async function loadAppointments() {\n      try {\n        setLoading(true)\n        const data = await api.getAppointments()\n        setAppointments(data)\n        setFilteredAppointments(data)\n\n        // Calculate stats\n        const appointmentStats: AppointmentStats = {\n          total: data.length,\n          pending: data.filter(apt => apt.status === 'pending').length,\n          confirmed: data.filter(apt => apt.status === 'confirmed').length,\n          completed: data.filter(apt => apt.status === 'completed').length,\n          cancelled: data.filter(apt => apt.status === 'cancelled').length,\n        }\n        setStats(appointmentStats)\n      } catch (error) {\n        console.error('Error loading appointments:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadAppointments()\n  }, [])\n\n  useEffect(() => {\n    let filtered = appointments\n\n    if (searchQuery) {\n      filtered = filtered.filter(appointment =>\n        appointment.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        appointment.phone?.includes(searchQuery) ||\n        appointment.service?.toLowerCase().includes(searchQuery.toLowerCase())\n      )\n    }\n\n    if (statusFilter !== 'all') {\n      filtered = filtered.filter(appointment => appointment.status === statusFilter)\n    }\n\n    setFilteredAppointments(filtered)\n  }, [appointments, searchQuery, statusFilter])\n\n  const handleRefresh = async () => {\n    try {\n      setLoading(true)\n      const data = await api.getAppointments()\n      setAppointments(data)\n      setFilteredAppointments(data)\n    } catch (error) {\n      console.error('Error refreshing appointments:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleViewDetails = (appointment: Appointment) => {\n    setSelectedAppointment(appointment)\n    setDetailsModalOpen(true)\n  }\n\n\n\n  if (loading) {\n    return (\n      <PageLayout\n        title=\"Appointments\"\n        description=\"Manage patient appointments and schedules\"\n        icon={Calendar}\n        actions={\n          <Button disabled className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 shadow-lg\">\n            <Plus className=\"mr-2 h-4 w-4\" />\n            New Appointment\n          </Button>\n        }\n        stats={\n          <StatsGrid>\n            <StatsCard title=\"Total Appointments\" value=\"...\" description=\"Loading...\" icon={Calendar} loading />\n            <StatsCard title=\"Pending\" value=\"...\" description=\"Loading...\" icon={Clock} loading />\n            <StatsCard title=\"Confirmed\" value=\"...\" description=\"Loading...\" icon={CheckCircle} loading />\n            <StatsCard title=\"Completed\" value=\"...\" description=\"Loading...\" icon={CheckCircle} loading />\n          </StatsGrid>\n        }\n      >\n        <div className=\"space-y-6\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <RefreshCw className=\"h-8 w-8 animate-spin mx-auto text-gray-400\" />\n                <p className=\"text-gray-500 mt-2\">Loading appointments...</p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </PageLayout>\n    )\n  }\n\n  return (\n    <PageLayout\n      title=\"Appointments\"\n      description=\"Manage patient appointments and schedules\"\n      icon={Calendar}\n      actions={\n        <div className=\"flex items-center gap-3\">\n          <Button onClick={handleRefresh} variant=\"outline\" size=\"sm\">\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            Refresh\n          </Button>\n          <Button className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 shadow-lg\">\n            <Plus className=\"h-4 w-4 mr-2\" />\n            New Appointment\n          </Button>\n        </div>\n      }\n      stats={\n        stats && (\n          <StatsGrid>\n            <StatsCard\n              title=\"Total Appointments\"\n              value={stats.total}\n              description=\"All appointments\"\n              icon={Calendar}\n              color=\"blue\"\n            />\n            <StatsCard\n              title=\"Pending\"\n              value={stats.pending}\n              description=\"Awaiting confirmation\"\n              icon={Clock}\n              color=\"yellow\"\n            />\n            <StatsCard\n              title=\"Confirmed\"\n              value={stats.confirmed}\n              description=\"Confirmed appointments\"\n              icon={CheckCircle}\n              color=\"green\"\n            />\n            <StatsCard\n              title=\"Completed\"\n              value={stats.completed}\n              description=\"Completed appointments\"\n              icon={CheckCircle}\n              color=\"blue\"\n            />\n          </StatsGrid>\n        )\n      }\n    >\n\n      {/* Filters */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">Filters</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n                <Input\n                  placeholder=\"Search appointments...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <Select value={statusFilter} onValueChange={setStatusFilter}>\n              <SelectTrigger className=\"w-full sm:w-[180px]\">\n                <SelectValue placeholder=\"Filter by status\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Status</SelectItem>\n                <SelectItem value=\"pending\">Pending</SelectItem>\n                <SelectItem value=\"confirmed\">Confirmed</SelectItem>\n                <SelectItem value=\"in_progress\">In Progress</SelectItem>\n                <SelectItem value=\"completed\">Completed</SelectItem>\n                <SelectItem value=\"cancelled\">Cancelled</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Appointments Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Appointments ({filteredAppointments.length})</CardTitle>\n          <CardDescription>\n            Recent appointments and their current status\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {filteredAppointments.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Calendar className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium\">No appointments found</h3>\n              <p className=\"text-muted-foreground\">\n                {searchQuery || statusFilter !== 'all' \n                  ? 'Try adjusting your filters' \n                  : 'No appointments have been scheduled yet'}\n              </p>\n            </div>\n          ) : (\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Patient</TableHead>\n                    <TableHead>Service</TableHead>\n                    <TableHead>Date & Time</TableHead>\n                    <TableHead>Status</TableHead>\n                    <TableHead>Contact</TableHead>\n                    <TableHead className=\"text-right\">Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {filteredAppointments.map((appointment) => (\n                    <TableRow key={appointment.id}>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          <User className=\"h-4 w-4 text-muted-foreground\" />\n                          <div>\n                            <div className=\"font-medium\">{appointment.name}</div>\n                            <div className=\"text-sm text-muted-foreground\">\n                              Appointment #{appointments.findIndex(a => a.id === appointment.id) + 1}\n                            </div>\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"font-medium\">{appointment.service}</div>\n                      </TableCell>\n                      <TableCell>\n                        <div>\n                          <div className=\"font-medium\">\n                            {formatDate(appointment.preferred_date)}\n                          </div>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {formatTime(appointment.preferred_time)}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Badge className={getStatusColor(appointment.status)}>\n                          <div className=\"flex items-center gap-1\">\n                            {getStatusIcon(appointment.status)}\n                            {appointment.status}\n                          </div>\n                        </Badge>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          <Phone className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"text-sm\">{appointment.phone}</span>\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        <div className=\"flex items-center justify-end space-x-2\">\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleViewDetails(appointment)}\n                          >\n                            <User className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Appointment Details Modal */}\n      <AppointmentDetailsModal\n        appointment={selectedAppointment}\n        open={detailsModalOpen}\n        onOpenChange={setDetailsModalOpen}\n      />\n    </PageLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAEA;AACA;AACA;;;AAtCA;;;;;;;;;;;;;AAwCA,oBAAoB;AACpB,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,6OAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,6LAAC,gNAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,6LAAC,6OAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,6LAAC,0NAAO;gBAAC,WAAU;;;;;;QAC5B,KAAK;YACH,qBAAO,6LAAC,sOAAW;gBAAC,WAAU;;;;;;QAChC;YACE,qBAAO,6LAAC,gNAAK;gBAAC,WAAU;;;;;;IAC5B;AACF;AAEA,MAAM,aAAa,CAAC;IAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEA,MAAM,aAAa,CAAC;IAClB,OAAO,IAAI,KAAK,AAAC,cAAwB,OAAX,aAAc,kBAAkB,CAAC,SAAS;QACtE,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAUO,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAgB,EAAE;IAClE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,yKAAQ,EAAgB,EAAE;IAClF,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAA0B;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,yKAAQ,EAAqB;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAC;IAEzD,IAAA,0KAAS;sCAAC;YACR,eAAe;gBACb,IAAI;oBACF,WAAW;oBACX,MAAM,OAAO,MAAM,qIAAG,CAAC,eAAe;oBACtC,gBAAgB;oBAChB,wBAAwB;oBAExB,kBAAkB;oBAClB,MAAM,mBAAqC;wBACzC,OAAO,KAAK,MAAM;wBAClB,SAAS,KAAK,MAAM;2EAAC,CAAA,MAAO,IAAI,MAAM,KAAK;0EAAW,MAAM;wBAC5D,WAAW,KAAK,MAAM;2EAAC,CAAA,MAAO,IAAI,MAAM,KAAK;0EAAa,MAAM;wBAChE,WAAW,KAAK,MAAM;2EAAC,CAAA,MAAO,IAAI,MAAM,KAAK;0EAAa,MAAM;wBAChE,WAAW,KAAK,MAAM;2EAAC,CAAA,MAAO,IAAI,MAAM,KAAK;0EAAa,MAAM;oBAClE;oBACA,SAAS;gBACX,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;qCAAG,EAAE;IAEL,IAAA,0KAAS;sCAAC;YACR,IAAI,WAAW;YAEf,IAAI,aAAa;gBACf,WAAW,SAAS,MAAM;kDAAC,CAAA;4BACzB,mBACA,oBACA;+BAFA,EAAA,oBAAA,YAAY,IAAI,cAAhB,wCAAA,kBAAkB,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,UAChE,qBAAA,YAAY,KAAK,cAAjB,yCAAA,mBAAmB,QAAQ,CAAC,mBAC5B,uBAAA,YAAY,OAAO,cAAnB,2CAAA,qBAAqB,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;;YAEvE;YAEA,IAAI,iBAAiB,OAAO;gBAC1B,WAAW,SAAS,MAAM;kDAAC,CAAA,cAAe,YAAY,MAAM,KAAK;;YACnE;YAEA,wBAAwB;QAC1B;qCAAG;QAAC;QAAc;QAAa;KAAa;IAE5C,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,qIAAG,CAAC,eAAe;YACtC,gBAAgB;YAChB,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,uBAAuB;QACvB,oBAAoB;IACtB;IAIA,IAAI,SAAS;QACX,qBACE,6LAAC,2JAAU;YACT,OAAM;YACN,aAAY;YACZ,MAAM,yNAAQ;YACd,uBACE,6LAAC,+IAAM;gBAAC,QAAQ;gBAAC,WAAU;;kCACzB,6LAAC,6MAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;YAIrC,qBACE,6LAAC,yJAAS;;kCACR,6LAAC,yJAAS;wBAAC,OAAM;wBAAqB,OAAM;wBAAM,aAAY;wBAAa,MAAM,yNAAQ;wBAAE,OAAO;;;;;;kCAClG,6LAAC,yJAAS;wBAAC,OAAM;wBAAU,OAAM;wBAAM,aAAY;wBAAa,MAAM,gNAAK;wBAAE,OAAO;;;;;;kCACpF,6LAAC,yJAAS;wBAAC,OAAM;wBAAY,OAAM;wBAAM,aAAY;wBAAa,MAAM,6OAAW;wBAAE,OAAO;;;;;;kCAC5F,6LAAC,yJAAS;wBAAC,OAAM;wBAAY,OAAM;wBAAM,aAAY;wBAAa,MAAM,6OAAW;wBAAE,OAAO;;;;;;;;;;;;sBAIhG,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2IAAI;8BACH,cAAA,6LAAC,kJAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gOAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhD;IAEA,qBACE,6LAAC,2JAAU;QACT,OAAM;QACN,aAAY;QACZ,MAAM,yNAAQ;QACd,uBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+IAAM;oBAAC,SAAS;oBAAe,SAAQ;oBAAU,MAAK;;sCACrD,6LAAC,gOAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGxC,6LAAC,+IAAM;oBAAC,WAAU;;sCAChB,6LAAC,6MAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;QAKvC,OACE,uBACE,6LAAC,yJAAS;;8BACR,6LAAC,yJAAS;oBACR,OAAM;oBACN,OAAO,MAAM,KAAK;oBAClB,aAAY;oBACZ,MAAM,yNAAQ;oBACd,OAAM;;;;;;8BAER,6LAAC,yJAAS;oBACR,OAAM;oBACN,OAAO,MAAM,OAAO;oBACpB,aAAY;oBACZ,MAAM,gNAAK;oBACX,OAAM;;;;;;8BAER,6LAAC,yJAAS;oBACR,OAAM;oBACN,OAAO,MAAM,SAAS;oBACtB,aAAY;oBACZ,MAAM,6OAAW;oBACjB,OAAM;;;;;;8BAER,6LAAC,yJAAS;oBACR,OAAM;oBACN,OAAO,MAAM,SAAS;oBACtB,aAAY;oBACZ,MAAM,6OAAW;oBACjB,OAAM;;;;;;;;;;;;;0BAQd,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;kCACT,cAAA,6LAAC,gJAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,6LAAC,kJAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,6IAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;8CAIhB,6LAAC,+IAAM;oCAAC,OAAO;oCAAc,eAAe;;sDAC1C,6LAAC,sJAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,oJAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,sJAAa;;8DACZ,6LAAC,mJAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,6LAAC,mJAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,6LAAC,mJAAU;oDAAC,OAAM;8DAAY;;;;;;8DAC9B,6LAAC,mJAAU;oDAAC,OAAM;8DAAc;;;;;;8DAChC,6LAAC,mJAAU;oDAAC,OAAM;8DAAY;;;;;;8DAC9B,6LAAC,mJAAU;oDAAC,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;;0CACT,6LAAC,gJAAS;;oCAAC;oCAAe,qBAAqB,MAAM;oCAAC;;;;;;;0CACtD,6LAAC,sJAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,kJAAW;kCACT,qBAAqB,MAAM,KAAK,kBAC/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yNAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,6LAAC;oCAAE,WAAU;8CACV,eAAe,iBAAiB,QAC7B,+BACA;;;;;;;;;;;iDAIR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6IAAK;;kDACJ,6LAAC,mJAAW;kDACV,cAAA,6LAAC,gJAAQ;;8DACP,6LAAC,iJAAS;8DAAC;;;;;;8DACX,6LAAC,iJAAS;8DAAC;;;;;;8DACX,6LAAC,iJAAS;8DAAC;;;;;;8DACX,6LAAC,iJAAS;8DAAC;;;;;;8DACX,6LAAC,iJAAS;8DAAC;;;;;;8DACX,6LAAC,iJAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,6LAAC,iJAAS;kDACP,qBAAqB,GAAG,CAAC,CAAC,4BACzB,6LAAC,gJAAQ;;kEACP,6LAAC,iJAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAe,YAAY,IAAI;;;;;;sFAC9C,6LAAC;4EAAI,WAAU;;gFAAgC;gFAC/B,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kEAK7E,6LAAC,iJAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;sEAAe,YAAY,OAAO;;;;;;;;;;;kEAEnD,6LAAC,iJAAS;kEACR,cAAA,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,WAAW,YAAY,cAAc;;;;;;8EAExC,6LAAC;oEAAI,WAAU;8EACZ,WAAW,YAAY,cAAc;;;;;;;;;;;;;;;;;kEAI5C,6LAAC,iJAAS;kEACR,cAAA,6LAAC,6IAAK;4DAAC,WAAW,eAAe,YAAY,MAAM;sEACjD,cAAA,6LAAC;gEAAI,WAAU;;oEACZ,cAAc,YAAY,MAAM;oEAChC,YAAY,MAAM;;;;;;;;;;;;;;;;;kEAIzB,6LAAC,iJAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EAAW,YAAY,KAAK;;;;;;;;;;;;;;;;;kEAGhD,6LAAC,iJAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,+IAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,kBAAkB;0EAEjC,cAAA,6LAAC,6MAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;+CA9CT,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4D3C,6LAAC,mMAAuB;gBACtB,aAAa;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB;GA3SgB;KAAA", "debugId": null}}]}