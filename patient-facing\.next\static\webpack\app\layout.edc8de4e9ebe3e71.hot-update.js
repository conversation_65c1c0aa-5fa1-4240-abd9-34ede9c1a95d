"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2d9c366805f0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGV0cmFcXE9uZURyaXZlXFxEZXNrdG9wXFxEZW50aXN0IHdlYnNpdGVcXHBhdGllbnQtZmFjaW5nXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmQ5YzM2NjgwNWYwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/appointment-form.tsx":
/*!*****************************************!*\
  !*** ./components/appointment-form.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentForm: () => (/* binding */ AppointmentForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/hydration-safe-input */ \"(app-pages-browser)/./components/ui/hydration-safe-input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AppointmentForm(param) {\n    let { onSuccess } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Basic Information\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        // Appointment Details\n        service: \"\",\n        preferred_date: \"\",\n        preferred_time: \"\",\n        message: \"\",\n        // Medical History\n        medical_history: \"\",\n        allergies: \"\",\n        current_medications: \"\",\n        previous_dental_work: \"\",\n        dental_concerns: \"\",\n        // Emergency Contact\n        emergency_contact_name: \"\",\n        emergency_contact_phone: \"\",\n        emergency_contact_relationship: \"\",\n        // Insurance Information (Optional)\n        has_insurance: \"\",\n        insurance_provider: \"\",\n        insurance_policy_number: \"\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const [availableTimeSlots, setAvailableTimeSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTimeSlot, setSelectedTimeSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSlots, setLoadingSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch available time slots when date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (formData.preferred_date) {\n                fetchAvailableSlots(formData.preferred_date);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        formData.preferred_date\n    ]);\n    const fetchAvailableSlots = async (date)=>{\n        setLoadingSlots(true);\n        try {\n            const response = await fetch(\"/api/availability?date=\".concat(date));\n            const data = await response.json();\n            if (data.available && data.timeSlots) {\n                // Transform time slots to include availability information\n                const slots = data.timeSlots.filter((slot)=>{\n                    const currentBookings = slot.current_appointments || 0;\n                    return currentBookings < slot.max_appointments;\n                }).map((slot)=>({\n                        time: slot.time,\n                        maxAppointments: slot.max_appointments,\n                        currentAppointments: slot.current_appointments || 0,\n                        availableSpots: slot.max_appointments - (slot.current_appointments || 0)\n                    }));\n                setAvailableTimeSlots(slots);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        } catch (error) {\n            console.error('Error fetching available slots:', error);\n            setAvailableTimeSlots([]);\n        } finally{\n            setLoadingSlots(false);\n        }\n    };\n    const services = [\n        \"✨ The Signature Glow\",\n        \"⚡ Express Refresh\",\n        \"💎 Complete Smile Makeover\",\n        \"🦷 Clear Aligners (Premium)\",\n        \"🔧 Root Canal Treatment\",\n        \"👑 Dental Crowns\",\n        \"🧽 Professional Cleaning\",\n        \"🦷 Dental Implants\",\n        \"Other (Please specify in message)\"\n    ];\n    const timeSlots = [\n        \"9:00 AM\",\n        \"10:00 AM\",\n        \"11:00 AM\",\n        \"12:00 PM\",\n        \"2:00 PM\",\n        \"3:00 PM\",\n        \"4:00 PM\",\n        \"5:00 PM\",\n        \"6:00 PM\",\n        \"7:00 PM\",\n        \"8:00 PM\"\n    ];\n    const handleChange = (e)=>{\n        const { id, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [id]: value\n            }));\n    };\n    const handleSelectChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // If selecting preferred_time, also store the slot details\n        if (field === 'preferred_time') {\n            const slot = availableTimeSlots.find((s)=>s.time === value);\n            setSelectedTimeSlot(slot || null);\n        }\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.name && formData.email && formData.phone && formData.date_of_birth;\n            case 2:\n                return formData.service && formData.preferred_date && formData.preferred_time;\n            case 3:\n                return formData.emergency_contact_name && formData.emergency_contact_phone;\n            case 4:\n                return true // Optional step\n                ;\n            default:\n                return false;\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            const response = await fetch('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setSubmitStatus('success');\n                setFormData({\n                    // Basic Information\n                    name: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    date_of_birth: \"\",\n                    address: \"\",\n                    // Appointment Details\n                    service: \"\",\n                    preferred_date: \"\",\n                    preferred_time: \"\",\n                    message: \"\",\n                    // Medical History\n                    medical_history: \"\",\n                    allergies: \"\",\n                    current_medications: \"\",\n                    previous_dental_work: \"\",\n                    dental_concerns: \"\",\n                    // Emergency Contact\n                    emergency_contact_name: \"\",\n                    emergency_contact_phone: \"\",\n                    emergency_contact_relationship: \"\",\n                    // Insurance Information (Optional)\n                    has_insurance: \"\",\n                    insurance_provider: \"\",\n                    insurance_policy_number: \"\"\n                });\n                setCurrentStep(1); // Reset to first step\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                setSubmitStatus('error');\n                console.error('Appointment booking error:', result.message);\n            }\n        } catch (error) {\n            setSubmitStatus('error');\n            console.error('Network error:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Get today's date in YYYY-MM-DD format for min date\n    const today = new Date().toISOString().split('T')[0];\n    const stepTitles = [\n        \"Personal Information\",\n        \"Appointment Details\",\n        \"Medical History & Emergency Contact\",\n        \"Insurance Information (Optional)\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: [\n                                    \"Step \",\n                                    currentStep,\n                                    \" of \",\n                                    totalSteps,\n                                    \": \",\n                                    stepTitles[currentStep - 1]\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    Math.round(currentStep / totalSteps * 100),\n                                    \"% Complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#6366F1] to-[#F59E0B] h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-green-50 border border-green-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-800 font-medium\",\n                    children: \"\\uD83C\\uDF89 Appointment booked successfully! We will call you within 2 hours to confirm your appointment.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this),\n            submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border border-red-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Sorry, there was an error booking your appointment. Please try again or call us directly at +91-11-41234567.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this),\n            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"name\",\n                                        className: \"text-foreground\",\n                                        children: \"Full Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"name\",\n                                        type: \"text\",\n                                        placeholder: \"Your Full Name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"email\",\n                                        className: \"text-foreground\",\n                                        children: \"Email *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        placeholder: \"<EMAIL>\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"phone\",\n                                        className: \"text-foreground\",\n                                        children: \"Phone Number *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"phone\",\n                                        type: \"tel\",\n                                        placeholder: \"+91-XXXXXXXXXX\",\n                                        value: formData.phone,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"date_of_birth\",\n                                        className: \"text-foreground\",\n                                        children: \"Date of Birth *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"date_of_birth\",\n                                        type: \"date\",\n                                        value: formData.date_of_birth,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"address\",\n                                className: \"text-foreground\",\n                                children: \"Address\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"address\",\n                                placeholder: \"Your complete address\",\n                                value: formData.address,\n                                onChange: handleChange,\n                                className: \"mt-1\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this),\n            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"service\",\n                                className: \"text-foreground\",\n                                children: \"Service Required *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                onValueChange: (value)=>handleSelectChange('service', value),\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Select a service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: service,\n                                                children: service\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_date\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Date *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"preferred_date\",\n                                        type: \"date\",\n                                        min: today,\n                                        value: formData.preferred_date,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_time\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Time *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        onValueChange: (value)=>handleSelectChange('preferred_time', value),\n                                        disabled: isSubmitting || loadingSlots || !formData.preferred_date,\n                                        value: formData.preferred_time,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: !formData.preferred_date ? \"Please select a date first\" : loadingSlots ? \"Loading available times...\" : availableTimeSlots.length === 0 ? \"No availability for this date\" : \"Select time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: availableTimeSlots.map((time)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: time,\n                                                        children: time\n                                                    }, time, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.preferred_date && !loadingSlots && availableTimeSlots.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600 mt-1\",\n                                        children: \"No availability for this date. Please select a different date.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"message\",\n                                className: \"text-foreground\",\n                                children: \"Additional Message\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"message\",\n                                placeholder: \"Any specific concerns or requirements? (Optional)\",\n                                value: formData.message,\n                                onChange: handleChange,\n                                className: \"mt-1 min-h-[100px]\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 366,\n                columnNumber: 9\n            }, this),\n            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Medical History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"medical_history\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"medical_history\",\n                                        placeholder: \"Please list any medical conditions, surgeries, or ongoing treatments\",\n                                        value: formData.medical_history,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"allergies\",\n                                        className: \"text-foreground\",\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"allergies\",\n                                        placeholder: \"Please list any allergies (medications, foods, materials, etc.)\",\n                                        value: formData.allergies,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"current_medications\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"current_medications\",\n                                        placeholder: \"Please list all medications you are currently taking\",\n                                        value: formData.current_medications,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"previous_dental_work\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Dental Work\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"previous_dental_work\",\n                                        placeholder: \"Please describe any previous dental treatments or procedures\",\n                                        value: formData.previous_dental_work,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"dental_concerns\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Dental Concerns\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"dental_concerns\",\n                                        placeholder: \"Please describe any current dental pain, concerns, or symptoms\",\n                                        value: formData.dental_concerns,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Emergency Contact *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_name\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_name\",\n                                                type: \"text\",\n                                                placeholder: \"Emergency contact full name\",\n                                                value: formData.emergency_contact_name,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_phone\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Phone *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_phone\",\n                                                type: \"tel\",\n                                                placeholder: \"+91-XXXXXXXXXX\",\n                                                value: formData.emergency_contact_phone,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"emergency_contact_relationship\",\n                                        className: \"text-foreground\",\n                                        children: \"Relationship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"emergency_contact_relationship\",\n                                        type: \"text\",\n                                        placeholder: \"e.g., Spouse, Parent, Sibling, Friend\",\n                                        value: formData.emergency_contact_relationship,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 458,\n                columnNumber: 9\n            }, this),\n            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-foreground\",\n                            children: \"Insurance Information (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"This information helps us process your treatment more efficiently, but it's completely optional.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"has_insurance\",\n                                    className: \"text-foreground\",\n                                    children: \"Do you have dental insurance?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: (value)=>handleSelectChange('has_insurance', value),\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                placeholder: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"yes\",\n                                                    children: \"Yes, I have dental insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"no\",\n                                                    children: \"No, I don't have insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"unsure\",\n                                                    children: \"I'm not sure\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 13\n                        }, this),\n                        formData.has_insurance === 'yes' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_provider\",\n                                            className: \"text-foreground\",\n                                            children: \"Insurance Provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_provider\",\n                                            type: \"text\",\n                                            placeholder: \"e.g., Star Health, HDFC ERGO, etc.\",\n                                            value: formData.insurance_provider,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_policy_number\",\n                                            className: \"text-foreground\",\n                                            children: \"Policy Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_policy_number\",\n                                            type: \"text\",\n                                            placeholder: \"Your insurance policy number\",\n                                            value: formData.insurance_policy_number,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 590,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6\",\n                children: [\n                    currentStep > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: prevStep,\n                        disabled: isSubmitting,\n                        className: \"px-6\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto\",\n                        children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            onClick: nextStep,\n                            disabled: !isStepValid(currentStep) || isSubmitting,\n                            className: \"px-6 bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white\",\n                            children: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            disabled: isSubmitting || !isStepValid(currentStep),\n                            className: \"px-8 rounded-2xl bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white font-semibold py-3 text-lg shadow-lg hover:from-[#5a5ee0] hover:to-[#e08d0a] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Booking Appointment...\"\n                                ]\n                            }, void 0, true) : 'Book Appointment'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 651,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-muted-foreground text-center\",\n                children: [\n                    \"* We will call you within 2 hours to confirm your appointment. For urgent needs, call us directly at\",\n                    ' ',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+911141234567\",\n                        className: \"text-primary hover:underline\",\n                        children: \"+91-11-41234567\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentForm, \"QAojgR/qBUp3v9H/PIaqF+OnFJw=\");\n_c = AppointmentForm;\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/appointment-form.tsx\n"));

/***/ })

});