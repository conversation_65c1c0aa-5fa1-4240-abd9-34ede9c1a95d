{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,mNAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mNAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mNAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/stats-cards.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Calendar,\n  Users,\n  IndianRupee,\n  UserCheck,\n  Clock,\n  TrendingUp,\n  TrendingDown,\n  ArrowUpRight,\n  ArrowDownRight,\n  MessageSquare\n} from 'lucide-react'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface StatsCardsProps {\n  stats: {\n    totalPatients: number\n    totalAppointments: number\n    totalServices: number\n    newMessages: number\n    pendingAppointments: number\n    confirmedAppointments: number\n    completedAppointments: number\n    inProgressAppointments: number\n  } | null\n  loading?: boolean\n}\n\nexport function StatsCards({ stats, loading = false }: StatsCardsProps) {\n  if (loading || !stats) {\n    return (\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n        {[...Array(4)].map((_, i) => (\n          <Card key={i} className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-2\">\n                  <div className=\"h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                  <div className=\"h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                </div>\n                <div className=\"h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse\" />\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    )\n  }\n\n  const cards = [\n    {\n      title: 'Total Patients',\n      value: stats.totalPatients.toString(),\n      description: 'Registered patients',\n      icon: Users,\n      trend: 0, // We'll calculate trends later\n      color: 'text-blue-600'\n    },\n    {\n      title: 'Total Appointments',\n      value: stats.totalAppointments.toString(),\n      description: 'All appointments',\n      icon: Calendar,\n      trend: 0,\n      color: 'text-green-600'\n    },\n    {\n      title: 'Pending Appointments',\n      value: stats.pendingAppointments.toString(),\n      description: 'Awaiting confirmation',\n      icon: Clock,\n      trend: 0,\n      color: 'text-orange-600'\n    },\n    {\n      title: 'New Messages',\n      value: stats.newMessages.toString(),\n      description: 'Unread messages',\n      icon: MessageSquare,\n      trend: 0,\n      color: 'text-purple-600'\n    },\n  ]\n\n  return (\n    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n      {cards.map((card, index) => {\n        const Icon = card.icon\n        const isPositiveTrend = card.trend > 0\n        const TrendIcon = isPositiveTrend ? ArrowUpRight : ArrowDownRight\n        \n        return (\n          <Card\n            key={index}\n            className=\"relative overflow-hidden bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040] hover:shadow-lg transition-all duration-200 group\"\n          >\n            {/* Gradient accent */}\n            <div className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-orange-600\" />\n\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {card.title}\n              </CardTitle>\n              <div className=\"p-2.5 rounded-xl bg-orange-50 dark:bg-orange-900/20 group-hover:scale-110 transition-transform duration-200\">\n                <Icon className=\"h-5 w-5 text-orange-600 dark:text-orange-400\" />\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                {card.value}\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {card.description}\n                </span>\n                <div className=\"flex items-center gap-1\">\n                  <TrendIcon className={`h-3 w-3 ${\n                    isPositiveTrend\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`} />\n                  <span className={`text-xs font-medium ${\n                    isPositiveTrend\n                      ? 'text-green-600 dark:text-green-400'\n                      : 'text-red-600 dark:text-red-400'\n                  }`}>\n                    {Math.abs(card.trend)}%\n                  </span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAgCO,SAAS,WAAW,EAAE,KAAK,EAAE,UAAU,KAAK,EAAmB;IACpE,IAAI,WAAW,CAAC,OAAO;QACrB,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,wIAAI;oBAAS,WAAU;8BACtB,cAAA,8OAAC,+IAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;mBAPV;;;;;;;;;;IAcnB;IAEA,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,aAAa;YACb,MAAM,6MAAK;YACX,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,iBAAiB,CAAC,QAAQ;YACvC,aAAa;YACb,MAAM,sNAAQ;YACd,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,mBAAmB,CAAC,QAAQ;YACzC,aAAa;YACb,MAAM,6MAAK;YACX,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW,CAAC,QAAQ;YACjC,aAAa;YACb,MAAM,yOAAa;YACnB,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,kBAAkB,KAAK,KAAK,GAAG;YACrC,MAAM,YAAY,kBAAkB,0OAAY,GAAG,gPAAc;YAEjE,qBACE,8OAAC,wIAAI;gBAEH,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC,8IAAU;wBAAC,WAAU;;0CACpB,8OAAC,6IAAS;gCAAC,WAAU;0CAClB,KAAK,KAAK;;;;;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;kCAGpB,8OAAC,+IAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK;;;;;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,KAAK,WAAW;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAU,WAAW,CAAC,QAAQ,EAC7B,kBACI,uCACA,kCACJ;;;;;;0DACF,8OAAC;gDAAK,WAAW,CAAC,oBAAoB,EACpC,kBACI,uCACA,kCACJ;;oDACC,KAAK,GAAG,CAAC,KAAK,KAAK;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;eAjCzB;;;;;QAwCX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/revenue-chart.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  AreaChart,\n  Area\n} from 'recharts'\nimport { formatCurrency } from '@/lib/utils'\n\n// Mock data for revenue chart\nconst revenueData = [\n  { month: 'Jan', revenue: 185000, appointments: 45 },\n  { month: 'Feb', revenue: 220000, appointments: 52 },\n  { month: 'Mar', revenue: 195000, appointments: 48 },\n  { month: 'Apr', revenue: 275000, appointments: 65 },\n  { month: 'May', revenue: 310000, appointments: 72 },\n  { month: 'Jun', revenue: 285000, appointments: 68 },\n  { month: 'Jul', revenue: 245000, appointments: 58 },\n]\n\nexport function RevenueChart() {\n  return (\n    <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n      <CardHeader>\n        <CardTitle className=\"text-gray-900 dark:text-white\">Revenue Overview</CardTitle>\n        <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n          Monthly revenue and appointment trends\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"h-[300px]\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <AreaChart data={revenueData}>\n              <defs>\n                <linearGradient id=\"revenueGradient\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                  <stop offset=\"5%\" stopColor=\"hsl(var(--primary))\" stopOpacity={0.3} />\n                  <stop offset=\"95%\" stopColor=\"hsl(var(--primary))\" stopOpacity={0} />\n                </linearGradient>\n              </defs>\n              <CartesianGrid strokeDasharray=\"3 3\" className=\"stroke-muted\" />\n              <XAxis \n                dataKey=\"month\" \n                className=\"text-xs fill-muted-foreground\"\n                axisLine={false}\n                tickLine={false}\n              />\n              <YAxis \n                className=\"text-xs fill-muted-foreground\"\n                axisLine={false}\n                tickLine={false}\n                tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}K`}\n              />\n              <Tooltip\n                content={({ active, payload, label }) => {\n                  if (active && payload && payload.length) {\n                    return (\n                      <div className=\"rounded-lg border bg-background p-3 shadow-md\">\n                        <p className=\"font-medium\">{label}</p>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Revenue: {formatCurrency(payload[0].value as number)}\n                        </p>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Appointments: {payload[0].payload.appointments}\n                        </p>\n                      </div>\n                    )\n                  }\n                  return null\n                }}\n              />\n              <Area\n                type=\"monotone\"\n                dataKey=\"revenue\"\n                stroke=\"hsl(var(--primary))\"\n                strokeWidth={2}\n                fill=\"url(#revenueGradient)\"\n              />\n            </AreaChart>\n          </ResponsiveContainer>\n        </div>\n        \n        {/* Summary Stats */}\n        <div className=\"mt-4 grid grid-cols-3 gap-4 border-t pt-4\">\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-green-600\">\n              {formatCurrency(revenueData.reduce((sum, item) => sum + item.revenue, 0))}\n            </p>\n            <p className=\"text-xs text-muted-foreground\">Total Revenue</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-blue-600\">\n              {formatCurrency(revenueData[revenueData.length - 1].revenue)}\n            </p>\n            <p className=\"text-xs text-muted-foreground\">This Month</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-purple-600\">\n              {formatCurrency(\n                revenueData.reduce((sum, item) => sum + item.revenue, 0) / revenueData.length\n              )}\n            </p>\n            <p className=\"text-xs text-muted-foreground\">Average</p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AAgBA,8BAA8B;AAC9B,MAAM,cAAc;IAClB;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;IAClD;QAAE,OAAO;QAAO,SAAS;QAAQ,cAAc;IAAG;CACnD;AAEM,SAAS;IACd,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;;kCACT,8OAAC,6IAAS;wBAAC,WAAU;kCAAgC;;;;;;kCACrD,8OAAC,mJAAe;wBAAC,WAAU;kCAAmC;;;;;;;;;;;;0BAIhE,8OAAC,+IAAW;;kCACV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAmB;4BAAC,OAAM;4BAAO,QAAO;sCACvC,cAAA,8OAAC,kKAAS;gCAAC,MAAM;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAe,IAAG;4CAAkB,IAAG;4CAAI,IAAG;4CAAI,IAAG;4CAAI,IAAG;;8DAC3D,8OAAC;oDAAK,QAAO;oDAAK,WAAU;oDAAsB,aAAa;;;;;;8DAC/D,8OAAC;oDAAK,QAAO;oDAAM,WAAU;oDAAsB,aAAa;;;;;;;;;;;;;;;;;kDAGpE,8OAAC,8KAAa;wCAAC,iBAAgB;wCAAM,WAAU;;;;;;kDAC/C,8OAAC,8JAAK;wCACJ,SAAQ;wCACR,WAAU;wCACV,UAAU;wCACV,UAAU;;;;;;kDAEZ,8OAAC,8JAAK;wCACJ,WAAU;wCACV,UAAU;wCACV,UAAU;wCACV,eAAe,CAAC,QAAU,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;kDAE5D,8OAAC,kKAAO;wCACN,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;4CAClC,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;gDACvC,qBACE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;;gEAAgC;gEACjC,IAAA,qIAAc,EAAC,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;sEAE3C,8OAAC;4DAAE,WAAU;;gEAAgC;gEAC5B,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY;;;;;;;;;;;;;4CAItD;4CACA,OAAO;wCACT;;;;;;kDAEF,8OAAC,4JAAI;wCACH,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,aAAa;wCACb,MAAK;;;;;;;;;;;;;;;;;;;;;;kCAOb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,IAAA,qIAAc,EAAC,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,OAAO,EAAE;;;;;;kDAExE,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAE/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,IAAA,qIAAc,EAAC,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,OAAO;;;;;;kDAE7D,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAE/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,IAAA,qIAAc,EACb,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,OAAO,EAAE,KAAK,YAAY,MAAM;;;;;;kDAGjF,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzD", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/appointment-chart.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from '@/components/ui/card'\nimport { \n  <PERSON><PERSON>hart, \n  Bar, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>,\n  Legend\n} from 'recharts'\n\n// Mock data for appointment status distribution\nconst appointmentStatusData = [\n  { name: 'Confirmed', value: 45, color: '#22c55e' },\n  { name: 'Pending', value: 12, color: '#f59e0b' },\n  { name: 'Completed', value: 78, color: '#3b82f6' },\n  { name: 'Cancelled', value: 8, color: '#ef4444' },\n]\n\n// Mock data for weekly appointments\nconst weeklyAppointments = [\n  { day: 'Mon', appointments: 12, completed: 10 },\n  { day: 'Tue', appointments: 15, completed: 13 },\n  { day: 'Wed', appointments: 8, completed: 7 },\n  { day: 'Thu', appointments: 18, completed: 16 },\n  { day: 'Fri', appointments: 22, completed: 20 },\n  { day: 'Sat', appointments: 16, completed: 14 },\n  { day: 'Sun', appointments: 6, completed: 5 },\n]\n\nexport function AppointmentChart() {\n  return (\n    <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n      <CardHeader>\n        <CardTitle className=\"text-gray-900 dark:text-white\">Appointment Analytics</CardTitle>\n        <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n          Weekly trends and status distribution\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-6\">\n          {/* Weekly Bar Chart */}\n          <div className=\"h-[200px]\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <BarChart data={weeklyAppointments}>\n                <CartesianGrid strokeDasharray=\"3 3\" className=\"stroke-muted\" />\n                <XAxis \n                  dataKey=\"day\" \n                  className=\"text-xs fill-muted-foreground\"\n                  axisLine={false}\n                  tickLine={false}\n                />\n                <YAxis \n                  className=\"text-xs fill-muted-foreground\"\n                  axisLine={false}\n                  tickLine={false}\n                />\n                <Tooltip\n                  content={({ active, payload, label }) => {\n                    if (active && payload && payload.length) {\n                      return (\n                        <div className=\"rounded-lg border bg-background p-3 shadow-md\">\n                          <p className=\"font-medium\">{label}</p>\n                          <p className=\"text-sm text-blue-600\">\n                            Scheduled: {payload[0].value}\n                          </p>\n                          <p className=\"text-sm text-green-600\">\n                            Completed: {payload[1].value}\n                          </p>\n                        </div>\n                      )\n                    }\n                    return null\n                  }}\n                />\n                <Bar \n                  dataKey=\"appointments\" \n                  fill=\"hsl(var(--primary))\" \n                  radius={[2, 2, 0, 0]}\n                  opacity={0.7}\n                />\n                <Bar \n                  dataKey=\"completed\" \n                  fill=\"hsl(var(--primary))\" \n                  radius={[2, 2, 0, 0]}\n                />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Status Distribution */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"h-[120px] w-[120px]\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <PieChart>\n                  <Pie\n                    data={appointmentStatusData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    innerRadius={30}\n                    outerRadius={50}\n                    paddingAngle={2}\n                    dataKey=\"value\"\n                  >\n                    {appointmentStatusData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color} />\n                    ))}\n                  </Pie>\n                  <Tooltip\n                    content={({ active, payload }) => {\n                      if (active && payload && payload.length) {\n                        return (\n                          <div className=\"rounded-lg border bg-background p-2 shadow-md\">\n                            <p className=\"text-sm font-medium\">\n                              {payload[0].payload.name}: {payload[0].value}\n                            </p>\n                          </div>\n                        )\n                      }\n                      return null\n                    }}\n                  />\n                </PieChart>\n              </ResponsiveContainer>\n            </div>\n            \n            <div className=\"flex-1 space-y-2 pl-4\">\n              {appointmentStatusData.map((item, index) => (\n                <div key={index} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <div \n                      className=\"h-3 w-3 rounded-full\" \n                      style={{ backgroundColor: item.color }}\n                    />\n                    <span className=\"text-sm text-muted-foreground\">\n                      {item.name}\n                    </span>\n                  </div>\n                  <span className=\"text-sm font-medium\">{item.value}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiBA,gDAAgD;AAChD,MAAM,wBAAwB;IAC5B;QAAE,MAAM;QAAa,OAAO;QAAI,OAAO;IAAU;IACjD;QAAE,MAAM;QAAW,OAAO;QAAI,OAAO;IAAU;IAC/C;QAAE,MAAM;QAAa,OAAO;QAAI,OAAO;IAAU;IACjD;QAAE,MAAM;QAAa,OAAO;QAAG,OAAO;IAAU;CACjD;AAED,oCAAoC;AACpC,MAAM,qBAAqB;IACzB;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAG,WAAW;IAAE;IAC5C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAI,WAAW;IAAG;IAC9C;QAAE,KAAK;QAAO,cAAc;QAAG,WAAW;IAAE;CAC7C;AAEM,SAAS;IACd,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;;kCACT,8OAAC,6IAAS;wBAAC,WAAU;kCAAgC;;;;;;kCACrD,8OAAC,mJAAe;wBAAC,WAAU;kCAAmC;;;;;;;;;;;;0BAIhE,8OAAC,+IAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,8OAAC,gKAAQ;oCAAC,MAAM;;sDACd,8OAAC,8KAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,8OAAC,8JAAK;4CACJ,SAAQ;4CACR,WAAU;4CACV,UAAU;4CACV,UAAU;;;;;;sDAEZ,8OAAC,8JAAK;4CACJ,WAAU;4CACV,UAAU;4CACV,UAAU;;;;;;sDAEZ,8OAAC,kKAAO;4CACN,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;gDAClC,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;oDACvC,qBACE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAe;;;;;;0EAC5B,8OAAC;gEAAE,WAAU;;oEAAwB;oEACvB,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;0EAE9B,8OAAC;gEAAE,WAAU;;oEAAyB;oEACxB,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;gDAIpC;gDACA,OAAO;4CACT;;;;;;sDAEF,8OAAC,0JAAG;4CACF,SAAQ;4CACR,MAAK;4CACL,QAAQ;gDAAC;gDAAG;gDAAG;gDAAG;6CAAE;4CACpB,SAAS;;;;;;sDAEX,8OAAC,0JAAG;4CACF,SAAQ;4CACR,MAAK;4CACL,QAAQ;gDAAC;gDAAG;gDAAG;gDAAG;6CAAE;;;;;;;;;;;;;;;;;;;;;;sCAO5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAmB;wCAAC,OAAM;wCAAO,QAAO;kDACvC,cAAA,8OAAC,gKAAQ;;8DACP,8OAAC,sJAAG;oDACF,MAAM;oDACN,IAAG;oDACH,IAAG;oDACH,aAAa;oDACb,aAAa;oDACb,cAAc;oDACd,SAAQ;8DAEP,sBAAsB,GAAG,CAAC,CAAC,OAAO,sBACjC,8OAAC,4JAAI;4DAAuB,MAAM,MAAM,KAAK;2DAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;8DAG9B,8OAAC,kKAAO;oDACN,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;wDAC3B,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;4DACvC,qBACE,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;wEACV,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI;wEAAC;wEAAG,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;wDAIpD;wDACA,OAAO;oDACT;;;;;;;;;;;;;;;;;;;;;;8CAMR,8OAAC;oCAAI,WAAU;8CACZ,sBAAsB,GAAG,CAAC,CAAC,MAAM,sBAChC,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,KAAK,KAAK;4DAAC;;;;;;sEAEvC,8OAAC;4DAAK,WAAU;sEACb,KAAK,IAAI;;;;;;;;;;;;8DAGd,8OAAC;oDAAK,WAAU;8DAAuB,KAAK,KAAK;;;;;;;2CAVzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmB1B", "debugId": null}}, {"offset": {"line": 1065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/recent-appointments.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport {\n  Calendar,\n  Clock,\n  Phone,\n  User,\n  MoreHorizontal,\n  CheckCircle,\n  XCircle,\n  Eye,\n} from 'lucide-react'\nimport { getStatusColor, formatTime } from '@/lib/utils'\nimport { type Appointment } from '@/lib/supabase'\n\ninterface RecentAppointmentsProps {\n  appointments: Appointment[]\n  loading?: boolean\n}\n\nconst getStatusIcon = (status: string) => {\n  switch (status) {\n    case 'confirmed':\n      return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n    case 'pending':\n      return <Clock className=\"h-4 w-4 text-yellow-600\" />\n    case 'completed':\n      return <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n    case 'cancelled':\n      return <XCircle className=\"h-4 w-4 text-red-600\" />\n    case 'in_progress':\n      return <Clock className=\"h-4 w-4 text-purple-600\" />\n    default:\n      return <Clock className=\"h-4 w-4 text-gray-600\" />\n  }\n}\n\nexport function RecentAppointments({ appointments, loading = false }: RecentAppointmentsProps) {\n  return (\n    <Card className=\"border-0 shadow-sm bg-white dark:bg-gray-900\">\n      <CardHeader className=\"pb-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n              Recent Appointments\n            </CardTitle>\n            <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n              Latest scheduled appointments\n            </CardDescription>\n          </div>\n          <Button variant=\"outline\" size=\"sm\">\n            <Calendar className=\"mr-2 h-4 w-4\" />\n            View All\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {loading ? (\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"flex items-center justify-between rounded-lg border p-4\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\" />\n                  <div className=\"space-y-2\">\n                    <div className=\"h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                    <div className=\"h-3 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                  <div className=\"h-3 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : appointments.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n            <Calendar className=\"h-12 w-12 text-muted-foreground/50\" />\n            <h3 className=\"mt-4 text-lg font-semibold\">No appointments</h3>\n            <p className=\"text-muted-foreground\">\n              No recent appointments to display.\n            </p>\n            <Button className=\"mt-4\">\n              <Calendar className=\"mr-2 h-4 w-4\" />\n              Schedule Appointment\n            </Button>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {appointments.slice(0, 5).map((appointment) => (\n              <div\n                key={appointment.id}\n                className=\"flex items-center justify-between rounded-lg border p-4 hover:bg-accent/50 transition-colors\"\n              >\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"flex h-10 w-10 items-center justify-center rounded-full bg-primary/10\">\n                    <User className=\"h-4 w-4 text-primary\" />\n                  </div>\n\n                  <div className=\"space-y-1\">\n                    <div className=\"flex items-center gap-2\">\n                      <p className=\"font-medium\">{appointment.full_name}</p>\n                      <Badge\n                        variant=\"outline\"\n                        className={getStatusColor(appointment.status)}\n                      >\n                        {appointment.status.replace('_', ' ')}\n                      </Badge>\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {appointment.service_type}\n                    </p>\n                    <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-3 w-3\" />\n                        {new Date(appointment.preferred_date).toLocaleDateString('en-IN')}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Clock className=\"h-3 w-3\" />\n                        {formatTime(appointment.preferred_time)}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Phone className=\"h-3 w-3\" />\n                        {appointment.phone}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"flex items-center gap-2\">\n                    {getStatusIcon(appointment.status)}\n                  </div>\n\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button variant=\"ghost\" size=\"icon\">\n                        <MoreHorizontal className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\">\n                      <DropdownMenuItem>\n                        <Eye className=\"mr-2 h-4 w-4\" />\n                        View Details\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <CheckCircle className=\"mr-2 h-4 w-4\" />\n                        Confirm\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Calendar className=\"mr-2 h-4 w-4\" />\n                        Reschedule\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Phone className=\"mr-2 h-4 w-4\" />\n                        Call Patient\n                      </DropdownMenuItem>\n                      <DropdownMenuItem className=\"text-red-600\">\n                        <XCircle className=\"mr-2 h-4 w-4\" />\n                        Cancel\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AArBA;;;;;;;;AA6BA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,0OAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC,6MAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,8OAAC,0OAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC,uNAAO;gBAAC,WAAU;;;;;;QAC5B,KAAK;YACH,qBAAO,8OAAC,6MAAK;gBAAC,WAAU;;;;;;QAC1B;YACE,qBAAO,8OAAC,6MAAK;gBAAC,WAAU;;;;;;IAC5B;AACF;AAEO,SAAS,mBAAmB,EAAE,YAAY,EAAE,UAAU,KAAK,EAA2B;IAC3F,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,6IAAS;oCAAC,WAAU;8CAAyD;;;;;;8CAG9E,8OAAC,mJAAe;oCAAC,WAAU;8CAAmC;;;;;;;;;;;;sCAIhE,8OAAC,4IAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,8OAAC,sNAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAK3C,8OAAC,+IAAW;0BACT,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAVT;;;;;;;;;2BAeZ,aAAa,MAAM,KAAK,kBAC1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sNAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC,4IAAM;4BAAC,WAAU;;8CAChB,8OAAC,sNAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;yCAKzC,8OAAC;oBAAI,WAAU;8BACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,4BAC7B,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAI;gDAAC,WAAU;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAe,YAAY,SAAS;;;;;;sEACjD,8OAAC,0IAAK;4DACJ,SAAQ;4DACR,WAAW,IAAA,qIAAc,EAAC,YAAY,MAAM;sEAE3C,YAAY,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8DAGrC,8OAAC;oDAAE,WAAU;8DACV,YAAY,YAAY;;;;;;8DAE3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sNAAQ;oEAAC,WAAU;;;;;;gEACnB,IAAI,KAAK,YAAY,cAAc,EAAE,kBAAkB,CAAC;;;;;;;sEAE3D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,6MAAK;oEAAC,WAAU;;;;;;gEAChB,IAAA,iIAAU,EAAC,YAAY,cAAc;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,6MAAK;oEAAC,WAAU;;;;;;gEAChB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;8CAM1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,cAAc,YAAY,MAAM;;;;;;sDAGnC,8OAAC,4JAAY;;8DACX,8OAAC,mKAAmB;oDAAC,OAAO;8DAC1B,cAAA,8OAAC,4IAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,8OAAC,kOAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,8OAAC,mKAAmB;oDAAC,OAAM;;sEACzB,8OAAC,gKAAgB;;8EACf,8OAAC,uMAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,8OAAC,gKAAgB;;8EACf,8OAAC,0OAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAG1C,8OAAC,gKAAgB;;8EACf,8OAAC,sNAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,8OAAC,gKAAgB;;8EACf,8OAAC,6MAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,8OAAC,gKAAgB;4DAAC,WAAU;;8EAC1B,8OAAC,uNAAO;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnEvC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;AAgFnC", "debugId": null}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/quick-actions.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  Calendar, \n  Users, \n  MessageSquare, \n  FileText,\n  UserPlus,\n  Bell,\n  Settings,\n  BarChart3,\n  Plus,\n  Search\n} from 'lucide-react'\n\nconst quickActions = [\n  {\n    title: 'New Appointment',\n    description: 'Schedule a new appointment',\n    icon: Calendar,\n    color: 'bg-blue-500 hover:bg-blue-600',\n    action: () => console.log('New appointment')\n  },\n  {\n    title: 'Add Patient',\n    description: 'Register new patient',\n    icon: UserPlus,\n    color: 'bg-green-500 hover:bg-green-600',\n    action: () => console.log('Add patient')\n  },\n  {\n    title: 'Send Message',\n    description: 'Contact patients',\n    icon: MessageSquare,\n    color: 'bg-purple-500 hover:bg-purple-600',\n    action: () => console.log('Send message')\n  },\n  {\n    title: 'Generate Report',\n    description: 'Create analytics report',\n    icon: BarChart3,\n    color: 'bg-orange-500 hover:bg-orange-600',\n    action: () => console.log('Generate report')\n  },\n]\n\nconst recentActions = [\n  {\n    title: 'Patient Search',\n    description: 'Find patient records quickly',\n    icon: Search,\n    href: '/patients'\n  },\n  {\n    title: 'Notifications',\n    description: 'View all notifications',\n    icon: Bell,\n    href: '/communications/notifications'\n  },\n  {\n    title: 'Reports',\n    description: 'Access detailed reports',\n    icon: FileText,\n    href: '/reports'\n  },\n  {\n    title: 'Settings',\n    description: 'Configure system settings',\n    icon: Settings,\n    href: '/settings'\n  },\n]\n\nexport function QuickActions() {\n  return (\n    <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n      <CardHeader className=\"pb-3\">\n        <CardTitle className=\"text-lg text-gray-900 dark:text-white\">Quick Actions</CardTitle>\n        <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n          Frequently used actions and shortcuts\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Primary Actions */}\n        <div className=\"grid grid-cols-2 gap-3\">\n          {quickActions.map((action, index) => {\n            const Icon = action.icon\n            return (\n              <Button\n                key={index}\n                variant=\"outline\"\n                className=\"h-auto flex-col gap-2 p-4 hover:shadow-md transition-all border-gray-200 dark:border-[#404040] hover:bg-gray-50 dark:hover:bg-[#2a2a2a] group\"\n                onClick={action.action}\n              >\n                <div className={`rounded-lg p-2 text-white ${action.color} group-hover:scale-110 transition-transform duration-200`}>\n                  <Icon className=\"h-4 w-4\" />\n                </div>\n                <div className=\"text-center\">\n                  <p className=\"text-xs font-medium text-gray-900 dark:text-white\">{action.title}</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {action.description}\n                  </p>\n                </div>\n              </Button>\n            )\n          })}\n        </div>\n\n        {/* Secondary Actions */}\n        <div className=\"space-y-2 border-t pt-4\">\n          <h4 className=\"text-sm font-medium text-muted-foreground\">\n            Quick Links\n          </h4>\n          {recentActions.map((action, index) => {\n            const Icon = action.icon\n            return (\n              <Button\n                key={index}\n                variant=\"ghost\"\n                className=\"w-full justify-start gap-3 h-auto p-3\"\n                asChild\n              >\n                <a href={action.href}>\n                  <Icon className=\"h-4 w-4 text-muted-foreground\" />\n                  <div className=\"text-left\">\n                    <p className=\"text-sm font-medium\">{action.title}</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {action.description}\n                    </p>\n                  </div>\n                </a>\n              </Button>\n            )\n          })}\n        </div>\n\n        {/* Emergency Actions */}\n        <div className=\"space-y-2 border-t pt-4\">\n          <h4 className=\"text-sm font-medium text-muted-foreground\">\n            Emergency\n          </h4>\n          <Button \n            variant=\"destructive\" \n            size=\"sm\" \n            className=\"w-full\"\n          >\n            <Plus className=\"mr-2 h-3 w-3\" />\n            Emergency Appointment\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAiBA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,aAAa;QACb,MAAM,sNAAQ;QACd,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,0NAAQ;QACd,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,yOAAa;QACnB,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,+NAAS;QACf,OAAO;QACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;IAC5B;CACD;AAED,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,aAAa;QACb,MAAM,gNAAM;QACZ,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,0MAAI;QACV,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,0NAAQ;QACd,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,sNAAQ;QACd,MAAM;IACR;CACD;AAEM,SAAS;IACd,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;gBAAC,WAAU;;kCACpB,8OAAC,6IAAS;wBAAC,WAAU;kCAAwC;;;;;;kCAC7D,8OAAC,mJAAe;wBAAC,WAAU;kCAAmC;;;;;;;;;;;;0BAIhE,8OAAC,+IAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,QAAQ;4BACzB,MAAM,OAAO,OAAO,IAAI;4BACxB,qBACE,8OAAC,4IAAM;gCAEL,SAAQ;gCACR,WAAU;gCACV,SAAS,OAAO,MAAM;;kDAEtB,8OAAC;wCAAI,WAAW,CAAC,0BAA0B,EAAE,OAAO,KAAK,CAAC,wDAAwD,CAAC;kDACjH,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqD,OAAO,KAAK;;;;;;0DAC9E,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;;;;;;;;+BAXlB;;;;;wBAgBX;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;4BAGzD,cAAc,GAAG,CAAC,CAAC,QAAQ;gCAC1B,MAAM,OAAO,OAAO,IAAI;gCACxB,qBACE,8OAAC,4IAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC;wCAAE,MAAM,OAAO,IAAI;;0DAClB,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuB,OAAO,KAAK;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCAVpB;;;;;4BAgBX;;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC,4IAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,8OAAC,0MAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/api-client.ts"], "sourcesContent": ["import { type Appointment, type Patient, type Service } from './supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\nclass ApiClient {\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    const response = await fetch(`/api${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    })\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  // Dashboard\n  async getDashboardStats(): Promise<DashboardStats> {\n    return this.request<DashboardStats>('/dashboard/stats')\n  }\n\n  // Appointments\n  async getAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments')\n  }\n\n  async getAppointment(id: string): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`)\n  }\n\n  async createAppointment(appointment: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(appointment),\n    })\n  }\n\n  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async getPendingAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments/pending')\n  }\n\n  async updatePendingAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/pending?id=${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Patients\n  async getPatients(): Promise<Patient[]> {\n    return this.request<Patient[]>('/patients')\n  }\n\n  async getPatient(id: string): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`)\n  }\n\n  async getPatientAppointments(patientId: string): Promise<any[]> {\n    return this.request<any[]>(`/patients/${patientId}/appointments`)\n  }\n\n  async createPatient(patient: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(patient),\n    })\n  }\n\n  async updatePatient(id: string, updates: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Services\n  async getServices(): Promise<Service[]> {\n    return this.request<Service[]>('/services')\n  }\n\n  async getService(id: string): Promise<Service> {\n    return this.request<Service>(`/services/${id}`)\n  }\n\n  async createService(service: Partial<Service>): Promise<Service> {\n    return this.request<Service>('/services', {\n      method: 'POST',\n      body: JSON.stringify(service),\n    })\n  }\n\n  async updateService(id: string, updates: Partial<Service>): Promise<Service> {\n    return this.request<Service>(`/services/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Reports\n  async getReportsData(dateRange?: string): Promise<any> {\n    const params = dateRange ? `?dateRange=${dateRange}` : ''\n    return this.request<any>(`/reports${params}`)\n  }\n\n  // Medical Records\n  async getMedicalRecords(patientId?: string): Promise<any[]> {\n    const params = patientId ? `?patientId=${patientId}` : ''\n    return this.request<any[]>(`/medical-records${params}`)\n  }\n\n  async getMedicalRecord(id: string): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`)\n  }\n\n  async createMedicalRecord(record: any): Promise<any> {\n    return this.request<any>('/medical-records', {\n      method: 'POST',\n      body: JSON.stringify(record),\n    })\n  }\n\n  async updateMedicalRecord(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Availability Slots\n  async getAvailabilitySlots(): Promise<any[]> {\n    return this.request<any[]>('/availability-slots')\n  }\n\n  async getAvailabilitySlot(id: string): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`)\n  }\n\n  async createAvailabilitySlot(slot: any): Promise<any> {\n    return this.request<any>('/availability-slots', {\n      method: 'POST',\n      body: JSON.stringify(slot),\n    })\n  }\n\n  async updateAvailabilitySlot(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async deleteAvailabilitySlot(id: string): Promise<void> {\n    return this.request<void>(`/availability-slots/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async checkSlotAvailability(date: string, time?: string): Promise<any> {\n    const params = time ? `?date=${date}&time=${time}` : `?date=${date}`\n    return this.request<any>(`/availability-slots/check${params}`)\n  }\n}\n\nexport const api = new ApiClient()\n"], "names": [], "mappings": ";;;;AAaA,MAAM;IACJ,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,MAAM,WAAW,MAAM,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE;YAC9C,SAAS;gBACP,gBAAgB;gBAChB,GAAG,SAAS,OAAO;YACrB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;QAC9D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,oBAA6C;QACjD,OAAO,IAAI,CAAC,OAAO,CAAiB;IACtC;IAEA,eAAe;IACf,MAAM,kBAA0C;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,eAAe,EAAU,EAAwB;QACrD,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,cAAc,EAAE,IAAI;IACxD;IAEA,MAAM,kBAAkB,WAAiC,EAAwB;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAc,iBAAiB;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,OAA6B,EAAwB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,cAAc,EAAE,IAAI,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAyB,EAAU,EAAE,OAA6B,EAAwB;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,yBAAyB,EAAE,IAAI,EAAE;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI;IAChD;IAEA,MAAM,uBAAuB,SAAiB,EAAkB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC;IAClE;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI;IAChD;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,UAAU;IACV,MAAM,eAAe,SAAkB,EAAgB;QACrD,MAAM,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACvD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,QAAQ;IAC9C;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,SAAkB,EAAkB;QAC1D,MAAM,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACvD,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,gBAAgB,EAAE,QAAQ;IACxD;IAEA,MAAM,iBAAiB,EAAU,EAAgB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,iBAAiB,EAAE,IAAI;IACnD;IAEA,MAAM,oBAAoB,MAAW,EAAgB;QACnD,OAAO,IAAI,CAAC,OAAO,CAAM,oBAAoB;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,oBAAoB,EAAU,EAAE,OAAY,EAAgB;QAChE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,iBAAiB,EAAE,IAAI,EAAE;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,uBAAuC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,MAAM,oBAAoB,EAAU,EAAgB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,oBAAoB,EAAE,IAAI;IACtD;IAEA,MAAM,uBAAuB,IAAS,EAAgB;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,uBAAuB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAE,OAAY,EAAgB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,oBAAoB,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAiB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,oBAAoB,EAAE,IAAI,EAAE;YACrD,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,IAAY,EAAE,IAAa,EAAgB;QACrE,MAAM,SAAS,OAAO,CAAC,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM;QACpE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,yBAAyB,EAAE,QAAQ;IAC/D;AACF;AAEO,MAAM,MAAM,IAAI", "debugId": null}}, {"offset": {"line": 2070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/dashboard/dashboard-overview.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Calendar } from 'lucide-react'\nimport { formatDate } from '@/lib/utils'\nimport { StatsCards } from './stats-cards'\nimport { RevenueChart } from './revenue-chart'\nimport { AppointmentChart } from './appointment-chart'\nimport { RecentAppointments } from './recent-appointments'\nimport { QuickActions } from './quick-actions'\n\n// Client-side component to handle date rendering and avoid hydration mismatch\nfunction TodayDate() {\n  const [currentDate, setCurrentDate] = useState<string>('')\n\n  useEffect(() => {\n    setCurrentDate(formatDate(new Date()))\n  }, [])\n\n  return <span>{currentDate}</span>\n}\nimport { api } from '@/lib/api-client'\nimport { type Appointment } from '@/lib/supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\n\n\nexport function DashboardOverview() {\n  const [currentTime, setCurrentTime] = useState(new Date())\n  const [stats, setStats] = useState<DashboardStats | null>(null)\n  const [appointments, setAppointments] = useState<Appointment[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date())\n    }, 1000)\n\n    return () => clearInterval(timer)\n  }, [])\n\n  useEffect(() => {\n    async function loadDashboardData() {\n      try {\n        setLoading(true)\n        const [statsData, appointmentsData] = await Promise.all([\n          api.getDashboardStats(),\n          api.getAppointments()\n        ])\n\n        setStats(statsData)\n        // Get recent appointments (last 5)\n        setAppointments(appointmentsData.slice(0, 5))\n      } catch (error) {\n        console.error('Error loading dashboard data:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadDashboardData()\n  }, [])\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 dark:text-white\">Dashboard</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n            Welcome back! Here&apos;s what&apos;s happening at your dental practice today.\n          </p>\n        </div>\n        <div className=\"flex items-center gap-4\">\n          <div className=\"text-right bg-white dark:bg-[#1a1a1a] rounded-xl p-4 border border-gray-200 dark:border-[#404040] shadow-sm\">\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">Current Time</p>\n            <p className=\"text-xl font-bold text-gray-900 dark:text-white\">\n              {currentTime.toLocaleTimeString('en-IN', {\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n              })}\n            </p>\n          </div>\n          <Button className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 shadow-lg px-6 py-3 h-auto\">\n            <Calendar className=\"mr-2 h-5 w-5\" />\n            New Appointment\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <StatsCards stats={stats} loading={loading} />\n\n      {/* Charts Row */}\n      <div className=\"grid gap-6 md:grid-cols-2\">\n        <RevenueChart />\n        <AppointmentChart />\n      </div>\n\n      {/* Main Content Grid */}\n      <div className=\"grid gap-6 md:grid-cols-3\">\n        {/* Recent Appointments */}\n        <div className=\"md:col-span-2\">\n          <RecentAppointments appointments={appointments} loading={loading} />\n        </div>\n\n        {/* Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Quick Actions */}\n          <QuickActions />\n\n          {/* Alerts & Notifications */}\n          <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg text-gray-900 dark:text-white\">Alerts & Notifications</CardTitle>\n              <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n                Important updates and reminders\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex items-center justify-center py-8 text-muted-foreground\">\n                <p>No alerts at this time</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                View All Notifications\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* Today's Schedule */}\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg\">Today&apos;s Schedule</CardTitle>\n              <CardDescription>\n                <TodayDate />\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">9:00 AM</span>\n                  <span>Priya Sharma - Checkup</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">11:30 AM</span>\n                  <span>Rajesh Kumar - Root Canal</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">2:00 PM</span>\n                  <span>Anita Patel - Whitening</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">4:30 PM</span>\n                  <span>Vikram Singh - Implants</span>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                <Calendar className=\"mr-2 h-3 w-3\" />\n                View Full Calendar\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAYA;AAvBA;;;;;;;;;;;;AAaA,8EAA8E;AAC9E,SAAS;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAS;IAEvD,IAAA,kNAAS,EAAC;QACR,eAAe,IAAA,iIAAU,EAAC,IAAI;IAChC,GAAG,EAAE;IAEL,qBAAO,8OAAC;kBAAM;;;;;;AAChB;;AAiBO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC,IAAI;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAwB;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAgB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,MAAM,QAAQ,YAAY;YACxB,eAAe,IAAI;QACrB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,IAAA,kNAAS,EAAC;QACR,eAAe;YACb,IAAI;gBACF,WAAW;gBACX,MAAM,CAAC,WAAW,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACtD,kIAAG,CAAC,iBAAiB;oBACrB,kIAAG,CAAC,eAAe;iBACpB;gBAED,SAAS;gBACT,mCAAmC;gBACnC,gBAAgB,iBAAiB,KAAK,CAAC,GAAG;YAC5C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAChF,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,8OAAC;wCAAE,WAAU;kDACV,YAAY,kBAAkB,CAAC,SAAS;4CACvC,MAAM;4CACN,QAAQ;4CACR,QAAQ;wCACV;;;;;;;;;;;;0CAGJ,8OAAC,4IAAM;gCAAC,WAAU;;kDAChB,8OAAC,sNAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,+JAAU;gBAAC,OAAO;gBAAO,SAAS;;;;;;0BAGnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,mKAAY;;;;;kCACb,8OAAC,2KAAgB;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+KAAkB;4BAAC,cAAc;4BAAc,SAAS;;;;;;;;;;;kCAI3D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,mKAAY;;;;;0CAGb,8OAAC,wIAAI;gCAAC,WAAU;;kDACd,8OAAC,8IAAU;wCAAC,WAAU;;0DACpB,8OAAC,6IAAS;gDAAC,WAAU;0DAAwC;;;;;;0DAC7D,8OAAC,mJAAe;gDAAC,WAAU;0DAAmC;;;;;;;;;;;;kDAIhE,8OAAC,+IAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;0DAEL,8OAAC,4IAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;0CAO3D,8OAAC,wIAAI;;kDACH,8OAAC,8IAAU;wCAAC,WAAU;;0DACpB,8OAAC,6IAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC,mJAAe;0DACd,cAAA,8OAAC;;;;;;;;;;;;;;;;kDAGL,8OAAC,+IAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC,4IAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,8OAAC,sNAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}]}