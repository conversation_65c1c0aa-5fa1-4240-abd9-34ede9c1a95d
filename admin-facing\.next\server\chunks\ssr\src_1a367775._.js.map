{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,mNAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mNAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mNAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as AvatarPrimitive from '@radix-ui/react-avatar'\n\nimport { cn } from '@/lib/utils'\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        'relative flex size-8 shrink-0 overflow-hidden rounded-full',\n        className,\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn('aspect-square size-full', className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        'bg-muted flex size-full items-center justify-center rounded-full',\n        className,\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,0KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,8KAAwB;QACvB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/api-client.ts"], "sourcesContent": ["import { type Appointment, type Patient, type Service } from './supabase'\n\ninterface DashboardStats {\n  totalPatients: number\n  totalAppointments: number\n  totalServices: number\n  newMessages: number\n  pendingAppointments: number\n  confirmedAppointments: number\n  completedAppointments: number\n  inProgressAppointments: number\n}\n\nclass ApiClient {\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    const response = await fetch(`/api${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    })\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  // Dashboard\n  async getDashboardStats(): Promise<DashboardStats> {\n    return this.request<DashboardStats>('/dashboard/stats')\n  }\n\n  // Appointments\n  async getAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments')\n  }\n\n  async getAppointment(id: string): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`)\n  }\n\n  async createAppointment(appointment: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(appointment),\n    })\n  }\n\n  async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async getPendingAppointments(): Promise<Appointment[]> {\n    return this.request<Appointment[]>('/appointments/pending')\n  }\n\n  async updatePendingAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {\n    return this.request<Appointment>(`/appointments/pending?id=${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Patients\n  async getPatients(): Promise<Patient[]> {\n    return this.request<Patient[]>('/patients')\n  }\n\n  async getPatient(id: string): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`)\n  }\n\n  async getPatientAppointments(patientId: string): Promise<any[]> {\n    return this.request<any[]>(`/patients/${patientId}/appointments`)\n  }\n\n  async createPatient(patient: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(patient),\n    })\n  }\n\n  async updatePatient(id: string, updates: Partial<Patient>): Promise<Patient> {\n    return this.request<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Services\n  async getServices(): Promise<Service[]> {\n    return this.request<Service[]>('/services')\n  }\n\n  async getService(id: string): Promise<Service> {\n    return this.request<Service>(`/services/${id}`)\n  }\n\n  async createService(service: Partial<Service>): Promise<Service> {\n    return this.request<Service>('/services', {\n      method: 'POST',\n      body: JSON.stringify(service),\n    })\n  }\n\n  async updateService(id: string, updates: Partial<Service>): Promise<Service> {\n    return this.request<Service>(`/services/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Reports\n  async getReportsData(dateRange?: string): Promise<any> {\n    const params = dateRange ? `?dateRange=${dateRange}` : ''\n    return this.request<any>(`/reports${params}`)\n  }\n\n  // Medical Records\n  async getMedicalRecords(patientId?: string): Promise<any[]> {\n    const params = patientId ? `?patientId=${patientId}` : ''\n    return this.request<any[]>(`/medical-records${params}`)\n  }\n\n  async getMedicalRecord(id: string): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`)\n  }\n\n  async createMedicalRecord(record: any): Promise<any> {\n    return this.request<any>('/medical-records', {\n      method: 'POST',\n      body: JSON.stringify(record),\n    })\n  }\n\n  async updateMedicalRecord(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/medical-records/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  // Availability Slots\n  async getAvailabilitySlots(): Promise<any[]> {\n    return this.request<any[]>('/availability-slots')\n  }\n\n  async getAvailabilitySlot(id: string): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`)\n  }\n\n  async createAvailabilitySlot(slot: any): Promise<any> {\n    return this.request<any>('/availability-slots', {\n      method: 'POST',\n      body: JSON.stringify(slot),\n    })\n  }\n\n  async updateAvailabilitySlot(id: string, updates: any): Promise<any> {\n    return this.request<any>(`/availability-slots/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n  }\n\n  async deleteAvailabilitySlot(id: string): Promise<void> {\n    return this.request<void>(`/availability-slots/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async checkSlotAvailability(date: string, time?: string): Promise<any> {\n    const params = time ? `?date=${date}&time=${time}` : `?date=${date}`\n    return this.request<any>(`/availability-slots/check${params}`)\n  }\n}\n\nexport const api = new ApiClient()\n"], "names": [], "mappings": ";;;;AAaA,MAAM;IACJ,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,MAAM,WAAW,MAAM,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE;YAC9C,SAAS;gBACP,gBAAgB;gBAChB,GAAG,SAAS,OAAO;YACrB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;QAC9D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,oBAA6C;QACjD,OAAO,IAAI,CAAC,OAAO,CAAiB;IACtC;IAEA,eAAe;IACf,MAAM,kBAA0C;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,eAAe,EAAU,EAAwB;QACrD,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,cAAc,EAAE,IAAI;IACxD;IAEA,MAAM,kBAAkB,WAAiC,EAAwB;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAc,iBAAiB;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,OAA6B,EAAwB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,cAAc,EAAE,IAAI,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAyB,EAAU,EAAE,OAA6B,EAAwB;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAc,CAAC,yBAAyB,EAAE,IAAI,EAAE;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI;IAChD;IAEA,MAAM,uBAAuB,SAAiB,EAAkB;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC;IAClE;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,WAAW;IACX,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAY;IACjC;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI;IAChD;IAEA,MAAM,cAAc,OAAyB,EAAoB;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAU,aAAa;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,UAAU;IACV,MAAM,eAAe,SAAkB,EAAgB;QACrD,MAAM,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACvD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,QAAQ;IAC9C;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,SAAkB,EAAkB;QAC1D,MAAM,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACvD,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,gBAAgB,EAAE,QAAQ;IACxD;IAEA,MAAM,iBAAiB,EAAU,EAAgB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,iBAAiB,EAAE,IAAI;IACnD;IAEA,MAAM,oBAAoB,MAAW,EAAgB;QACnD,OAAO,IAAI,CAAC,OAAO,CAAM,oBAAoB;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,oBAAoB,EAAU,EAAE,OAAY,EAAgB;QAChE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,iBAAiB,EAAE,IAAI,EAAE;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,uBAAuC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,MAAM,oBAAoB,EAAU,EAAgB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,oBAAoB,EAAE,IAAI;IACtD;IAEA,MAAM,uBAAuB,IAAS,EAAgB;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,uBAAuB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAE,OAAY,EAAgB;QACnE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,oBAAoB,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,uBAAuB,EAAU,EAAiB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,oBAAoB,EAAE,IAAI,EAAE;YACrD,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,IAAY,EAAE,IAAa,EAAgB;QACrE,MAAM,SAAS,OAAO,CAAC,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM;QACpE,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,yBAAyB,EAAE,QAAQ;IAC/D;AACF;AAEO,MAAM,MAAM,IAAI", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,0KAAoB;AAEnC,MAAM,gBAAgB,6KAAuB;AAE7C,MAAM,eAAe,4KAAsB;AAE3C,MAAM,cAAc,2KAAqB;AAEzC,MAAM,8BAAgB,mNAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,6KAAuB;QACtB,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,6KAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,mNAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,6KAAuB;gBACtB,KAAK;gBACL,WAAW,IAAA,yHAAE,EACX,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,2KAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,iMAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,6KAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,IAAA,yHAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,IAAA,yHAAE,EACX,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAqB;QACpB,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,2KAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,mNAAgB,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iLAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,iLAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/patients/patient-details-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport {\n  User,\n  Phone,\n  Mail,\n  Calendar,\n  MapPin,\n  Activity,\n  FileText,\n  Clock,\n  Edit,\n  Trash2,\n  Plus\n} from 'lucide-react'\nimport { type Patient } from '@/lib/supabase'\nimport { api } from '@/lib/api-client'\n\ninterface PatientDetailsModalProps {\n  patient: Patient | null\n  open: boolean\n  onOpenChange: (open: boolean) => void\n}\n\ninterface PatientAppointment {\n  id: string\n  service_name: string\n  appointment_date: string\n  appointment_time: string\n  status: string\n  notes?: string\n}\n\nexport function PatientDetailsModal({ patient, open, onOpenChange }: PatientDetailsModalProps) {\n  const [appointments, setAppointments] = useState<PatientAppointment[]>([])\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    if (patient && open) {\n      fetchPatientAppointments()\n    }\n  }, [patient, open])\n\n  const fetchPatientAppointments = async () => {\n    if (!patient) return\n\n    setLoading(true)\n    try {\n      // Fetch real appointments for this patient\n      const data = await api.getPatientAppointments(patient.id)\n      const formattedAppointments: PatientAppointment[] = data.map((appointment: any) => ({\n        id: appointment.id,\n        service_name: appointment.service,\n        appointment_date: appointment.preferred_date,\n        appointment_time: appointment.preferred_time,\n        status: appointment.status,\n        notes: appointment.notes || appointment.message || ''\n      }))\n      setAppointments(formattedAppointments)\n    } catch (error) {\n      console.error('Error fetching patient appointments:', error)\n      setAppointments([])\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const calculateAge = (dateOfBirth: string) => {\n    const today = new Date()\n    const birthDate = new Date(dateOfBirth)\n    let age = today.getFullYear() - birthDate.getFullYear()\n    const monthDiff = today.getMonth() - birthDate.getMonth()\n    \n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n      age--\n    }\n    \n    return age\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n      case 'confirmed':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'\n      case 'cancelled':\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n    }\n  }\n\n  if (!patient) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center space-x-3\">\n            <Avatar className=\"h-12 w-12\">\n              <AvatarImage src={patient.avatar_url || undefined} />\n              <AvatarFallback className=\"bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300\">\n                {getInitials(patient.full_name || 'Unknown')}\n              </AvatarFallback>\n            </Avatar>\n            <div>\n              <div className=\"text-xl font-semibold\">{patient.full_name}</div>\n              <div className=\"text-sm text-muted-foreground\">Patient Profile</div>\n            </div>\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"grid gap-6 md:grid-cols-2\">\n          {/* Personal Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <User className=\"h-5 w-5\" />\n                <span>Personal Information</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3\">\n                <Mail className=\"h-4 w-4 text-muted-foreground\" />\n                <div>\n                  <div className=\"text-sm font-medium\">Email</div>\n                  <div className=\"text-sm text-muted-foreground\">{patient.email}</div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <Phone className=\"h-4 w-4 text-muted-foreground\" />\n                <div>\n                  <div className=\"text-sm font-medium\">Phone</div>\n                  <div className=\"text-sm text-muted-foreground\">{patient.phone}</div>\n                </div>\n              </div>\n\n              {patient.date_of_birth && (\n                <div className=\"flex items-center space-x-3\">\n                  <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n                  <div>\n                    <div className=\"text-sm font-medium\">Age</div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {calculateAge(patient.date_of_birth)} years old\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {patient.address && (\n                <div className=\"flex items-center space-x-3\">\n                  <MapPin className=\"h-4 w-4 text-muted-foreground\" />\n                  <div>\n                    <div className=\"text-sm font-medium\">Address</div>\n                    <div className=\"text-sm text-muted-foreground\">{patient.address}</div>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Medical Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Activity className=\"h-5 w-5\" />\n                <span>Medical Information</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <div className=\"text-sm font-medium\">Medical History</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.medical_history || 'No medical history recorded'}\n                </div>\n              </div>\n\n              <div>\n                <div className=\"text-sm font-medium\">Allergies</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.allergies || 'No known allergies'}\n                </div>\n              </div>\n\n              <div>\n                <div className=\"text-sm font-medium\">Current Medications</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.current_medications || 'No current medications'}\n                </div>\n              </div>\n\n              <div>\n                <div className=\"text-sm font-medium\">Previous Dental Work</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.previous_dental_work || 'No previous dental work recorded'}\n                </div>\n              </div>\n\n              <div>\n                <div className=\"text-sm font-medium\">Dental Concerns</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.dental_concerns || 'No specific concerns noted'}\n                </div>\n              </div>\n\n              <div>\n                <div className=\"text-sm font-medium\">Emergency Contact</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.emergency_contact ? (\n                    <div>\n                      <div>{patient.emergency_contact}</div>\n                      {patient.emergency_contact_phone && (\n                        <div className=\"text-xs\">{patient.emergency_contact_phone}</div>\n                      )}\n                      {patient.emergency_contact_relationship && (\n                        <div className=\"text-xs\">({patient.emergency_contact_relationship})</div>\n                      )}\n                    </div>\n                  ) : (\n                    'Not provided'\n                  )}\n                </div>\n              </div>\n\n              <div>\n                <div className=\"text-sm font-medium\">Insurance Information</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.has_insurance === 'yes' ? (\n                    <div>\n                      <div>Has Insurance: Yes</div>\n                      {patient.insurance_provider && (\n                        <div className=\"text-xs\">Provider: {patient.insurance_provider}</div>\n                      )}\n                      {patient.insurance_policy_number && (\n                        <div className=\"text-xs\">Policy: {patient.insurance_policy_number}</div>\n                      )}\n                    </div>\n                  ) : patient.has_insurance === 'no' ? (\n                    'No insurance'\n                  ) : patient.has_insurance === 'unsure' ? (\n                    'Insurance status unsure'\n                  ) : (\n                    'Insurance information not provided'\n                  )}\n                </div>\n              </div>\n\n              <div>\n                <div className=\"text-sm font-medium\">Last Visit</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {patient.last_visit\n                    ? new Date(patient.last_visit).toLocaleDateString()\n                    : 'Never'\n                  }\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Appointment History */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <FileText className=\"h-5 w-5\" />\n                <span>Appointment History</span>\n              </div>\n              <Button size=\"sm\">\n                <Plus className=\"h-4 w-4 mr-2\" />\n                New Appointment\n              </Button>\n            </CardTitle>\n            <CardDescription>\n              Recent appointments and treatments\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"space-y-3\">\n                {[...Array(3)].map((_, i) => (\n                  <div key={i} className=\"h-16 bg-muted animate-pulse rounded-lg\" />\n                ))}\n              </div>\n            ) : appointments.length === 0 ? (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <Calendar className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>No appointments found</p>\n                <p className=\"text-sm\">This patient hasn&apos;t had any appointments yet</p>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {appointments.map((appointment) => (\n                  <div key={appointment.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Clock className=\"h-4 w-4 text-muted-foreground\" />\n                        <div>\n                          <div className=\"font-medium\">{appointment.service_name}</div>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {new Date(appointment.appointment_date).toLocaleDateString()} at {appointment.appointment_time}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <Badge className={getStatusColor(appointment.status)}>\n                        {appointment.status}\n                      </Badge>\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <Edit className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-2 pt-4 border-t\">\n          <Button variant=\"outline\">\n            <Edit className=\"h-4 w-4 mr-2\" />\n            Edit Patient\n          </Button>\n          <Button variant=\"outline\">\n            <FileText className=\"h-4 w-4 mr-2\" />\n            Medical Records\n          </Button>\n          <Button variant=\"destructive\">\n            <Trash2 className=\"h-4 w-4 mr-2\" />\n            Delete Patient\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AA5BA;;;;;;;;;;AA6CO,SAAS,oBAAoB,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAA4B;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAuB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,IAAI,WAAW,MAAM;YACnB;QACF;IACF,GAAG;QAAC;QAAS;KAAK;IAElB,MAAM,2BAA2B;QAC/B,IAAI,CAAC,SAAS;QAEd,WAAW;QACX,IAAI;YACF,2CAA2C;YAC3C,MAAM,OAAO,MAAM,kIAAG,CAAC,sBAAsB,CAAC,QAAQ,EAAE;YACxD,MAAM,wBAA8C,KAAK,GAAG,CAAC,CAAC,cAAqB,CAAC;oBAClF,IAAI,YAAY,EAAE;oBAClB,cAAc,YAAY,OAAO;oBACjC,kBAAkB,YAAY,cAAc;oBAC5C,kBAAkB,YAAY,cAAc;oBAC5C,QAAQ,YAAY,MAAM;oBAC1B,OAAO,YAAY,KAAK,IAAI,YAAY,OAAO,IAAI;gBACrD,CAAC;YACD,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,gBAAgB,EAAE;QACpB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,IAAI,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;QACrD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;QAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;YAC/E;QACF;QAEA,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC,4IAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,mJAAa;YAAC,WAAU;;8BACvB,8OAAC,kJAAY;8BACX,cAAA,8OAAC,iJAAW;wBAAC,WAAU;;0CACrB,8OAAC,4IAAM;gCAAC,WAAU;;kDAChB,8OAAC,iJAAW;wCAAC,KAAK,QAAQ,UAAU,IAAI;;;;;;kDACxC,8OAAC,oJAAc;wCAAC,WAAU;kDACvB,YAAY,QAAQ,SAAS,IAAI;;;;;;;;;;;;0CAGtC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAyB,QAAQ,SAAS;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;8BAKrD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,wIAAI;;8CACH,8OAAC,8IAAU;8CACT,cAAA,8OAAC,6IAAS;wCAAC,WAAU;;0DACnB,8OAAC,0MAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC,+IAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;sEAAiC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;sDAIjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6MAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;sEAAiC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;wCAIhE,QAAQ,aAAa,kBACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;;gEACZ,aAAa,QAAQ,aAAa;gEAAE;;;;;;;;;;;;;;;;;;;wCAM5C,QAAQ,OAAO,kBACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oNAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;sEAAiC,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzE,8OAAC,wIAAI;;8CACH,8OAAC,8IAAU;8CACT,cAAA,8OAAC,6IAAS;wCAAC,WAAU;;0DACnB,8OAAC,sNAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC,+IAAW;oCAAC,WAAU;;sDACrB,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,eAAe,IAAI;;;;;;;;;;;;sDAIhC,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,SAAS,IAAI;;;;;;;;;;;;sDAI1B,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,mBAAmB,IAAI;;;;;;;;;;;;sDAIpC,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,oBAAoB,IAAI;;;;;;;;;;;;sDAIrC,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,eAAe,IAAI;;;;;;;;;;;;sDAIhC,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,iBAAiB,iBACxB,8OAAC;;0EACC,8OAAC;0EAAK,QAAQ,iBAAiB;;;;;;4DAC9B,QAAQ,uBAAuB,kBAC9B,8OAAC;gEAAI,WAAU;0EAAW,QAAQ,uBAAuB;;;;;;4DAE1D,QAAQ,8BAA8B,kBACrC,8OAAC;gEAAI,WAAU;;oEAAU;oEAAE,QAAQ,8BAA8B;oEAAC;;;;;;;;;;;;+DAItE;;;;;;;;;;;;sDAKN,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,aAAa,KAAK,sBACzB,8OAAC;;0EACC,8OAAC;0EAAI;;;;;;4DACJ,QAAQ,kBAAkB,kBACzB,8OAAC;gEAAI,WAAU;;oEAAU;oEAAW,QAAQ,kBAAkB;;;;;;;4DAE/D,QAAQ,uBAAuB,kBAC9B,8OAAC;gEAAI,WAAU;;oEAAU;oEAAS,QAAQ,uBAAuB;;;;;;;;;;;;+DAGnE,QAAQ,aAAa,KAAK,OAC5B,iBACE,QAAQ,aAAa,KAAK,WAC5B,4BAEA;;;;;;;;;;;;sDAKN,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,UAAU,GACf,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,KAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASd,8OAAC,wIAAI;;sCACH,8OAAC,8IAAU;;8CACT,8OAAC,6IAAS;oCAAC,WAAU;;sDACnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0NAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,4IAAM;4CAAC,MAAK;;8DACX,8OAAC,0MAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAIrC,8OAAC,mJAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,+IAAW;sCACT,wBACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wCAAY,WAAU;uCAAb;;;;;;;;;uCAGZ,aAAa,MAAM,KAAK,kBAC1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;qDAGzB,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;wCAAyB,WAAU;;0DAClC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6MAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAe,YAAY,YAAY;;;;;;8EACtD,8OAAC;oEAAI,WAAU;;wEACZ,IAAI,KAAK,YAAY,gBAAgB,EAAE,kBAAkB;wEAAG;wEAAK,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;0DAKtG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0IAAK;wDAAC,WAAW,eAAe,YAAY,MAAM;kEAChD,YAAY,MAAM;;;;;;kEAErB,8OAAC,4IAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,8OAAC,mNAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;uCAjBZ,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;8BA4BlC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4IAAM;4BAAC,SAAQ;;8CACd,8OAAC,mNAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGnC,8OAAC,4IAAM;4BAAC,SAAQ;;8CACd,8OAAC,0NAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGvC,8OAAC,4IAAM;4BAAC,SAAQ;;8CACd,8OAAC,oNAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 1543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/stats-card.tsx"], "sourcesContent": ["import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { LucideIcon, ArrowUpRight, ArrowDownRight } from 'lucide-react'\n\ninterface StatsCardProps {\n  title: string\n  value: string | number\n  description?: string\n  icon: LucideIcon\n  trend?: number\n  loading?: boolean\n  color?: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'yellow'\n}\n\nexport function StatsCard({ \n  title, \n  value, \n  description, \n  icon: Icon, \n  trend, \n  loading = false,\n  color = 'orange'\n}: StatsCardProps) {\n  if (loading) {\n    return (\n      <Card className=\"bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040]\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-2\">\n              <div className=\"h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n              <div className=\"h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\" />\n            </div>\n            <div className=\"h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse\" />\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  const colorClasses = {\n    blue: {\n      accent: 'from-blue-500 to-blue-600',\n      icon: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'\n    },\n    green: {\n      accent: 'from-green-500 to-green-600',\n      icon: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400'\n    },\n    orange: {\n      accent: 'from-orange-500 to-orange-600',\n      icon: 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400'\n    },\n    purple: {\n      accent: 'from-purple-500 to-purple-600',\n      icon: 'bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'\n    },\n    red: {\n      accent: 'from-red-500 to-red-600',\n      icon: 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400'\n    },\n    yellow: {\n      accent: 'from-yellow-500 to-yellow-600',\n      icon: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400'\n    }\n  }\n\n  const isPositiveTrend = trend !== undefined && trend > 0\n  const TrendIcon = isPositiveTrend ? ArrowUpRight : ArrowDownRight\n\n  return (\n    <Card className=\"relative overflow-hidden bg-white dark:bg-[#1a1a1a] border-gray-200 dark:border-[#404040] hover:shadow-lg transition-all duration-200 group\">\n      {/* Gradient accent */}\n      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${colorClasses[color].accent}`} />\n\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n        <CardTitle className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n          {title}\n        </CardTitle>\n        <div className={`p-2.5 rounded-xl ${colorClasses[color].icon} group-hover:scale-110 transition-transform duration-200`}>\n          <Icon className=\"h-5 w-5\" />\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-3\">\n        <div className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          {value}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          {description && (\n            <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n              {description}\n            </span>\n          )}\n          {trend !== undefined && (\n            <div className=\"flex items-center gap-1\">\n              <TrendIcon className={`h-3 w-3 ${\n                isPositiveTrend\n                  ? 'text-green-600 dark:text-green-400'\n                  : 'text-red-600 dark:text-red-400'\n              }`} />\n              <span className={`text-xs font-medium ${\n                isPositiveTrend\n                  ? 'text-green-600 dark:text-green-400'\n                  : 'text-red-600 dark:text-red-400'\n              }`}>\n                {Math.abs(trend)}%\n              </span>\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\ninterface StatsGridProps {\n  children: React.ReactNode\n  columns?: 2 | 3 | 4\n}\n\nexport function StatsGrid({ children, columns = 4 }: StatsGridProps) {\n  const gridClasses = {\n    2: 'grid gap-6 md:grid-cols-2',\n    3: 'grid gap-6 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid gap-6 md:grid-cols-2 lg:grid-cols-4'\n  }\n\n  return (\n    <div className={gridClasses[columns]}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;;;;AAYO,SAAS,UAAU,EACxB,KAAK,EACL,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,QAAQ,EACD;IACf,IAAI,SAAS;QACX,qBACE,8OAAC,wIAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,+IAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,MAAM,eAAe;QACnB,MAAM;YACJ,QAAQ;YACR,MAAM;QACR;QACA,OAAO;YACL,QAAQ;YACR,MAAM;QACR;QACA,QAAQ;YACN,QAAQ;YACR,MAAM;QACR;QACA,QAAQ;YACN,QAAQ;YACR,MAAM;QACR;QACA,KAAK;YACH,QAAQ;YACR,MAAM;QACR;QACA,QAAQ;YACN,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,UAAU,aAAa,QAAQ;IACvD,MAAM,YAAY,kBAAkB,0OAAY,GAAG,gPAAc;IAEjE,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BAEd,8OAAC;gBAAI,WAAW,CAAC,kDAAkD,EAAE,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE;;;;;;0BAEjG,8OAAC,8IAAU;gBAAC,WAAU;;kCACpB,8OAAC,6IAAS;wBAAC,WAAU;kCAClB;;;;;;kCAEH,8OAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC;kCACpH,cAAA,8OAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;0BAGpB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC;gCAAK,WAAU;0CACb;;;;;;4BAGJ,UAAU,2BACT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAU,WAAW,CAAC,QAAQ,EAC7B,kBACI,uCACA,kCACJ;;;;;;kDACF,8OAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,kBACI,uCACA,kCACJ;;4CACC,KAAK,GAAG,CAAC;4CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC;AAOO,SAAS,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAkB;IACjE,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAW,WAAW,CAAC,QAAQ;kBACjC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/patients/patients-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  Search,\n  Plus,\n  RefreshCw,\n  User,\n  Phone,\n  Mail,\n  Calendar,\n  MapPin,\n  Users,\n  Eye,\n  Edit,\n  Trash2,\n} from 'lucide-react'\nimport { api } from '@/lib/api-client'\nimport { type Patient } from '@/lib/supabase'\nimport { PatientDetailsModal } from './patient-details-modal'\nimport { PageLayout } from '@/components/ui/page-layout'\nimport { StatsCard, StatsGrid } from '@/components/ui/stats-card'\n\n// Utility functions\nconst getInitials = (name: string) => {\n  return name\n    .split(' ')\n    .map(n => n[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nconst formatDate = (dateString: string) => {\n  return new Date(dateString).toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  })\n}\n\nconst calculateAge = (dateOfBirth: string) => {\n  const today = new Date()\n  const birthDate = new Date(dateOfBirth)\n  let age = today.getFullYear() - birthDate.getFullYear()\n  const monthDiff = today.getMonth() - birthDate.getMonth()\n  \n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n    age--\n  }\n  \n  return age\n}\n\ninterface PatientStats {\n  total: number\n  newThisMonth: number\n  activePatients: number\n  totalAppointments: number\n}\n\nexport function PatientsPage() {\n  const [patients, setPatients] = useState<Patient[]>([])\n  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([])\n  const [stats, setStats] = useState<PatientStats | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)\n  const [detailsModalOpen, setDetailsModalOpen] = useState(false)\n\n  useEffect(() => {\n    async function loadPatients() {\n      try {\n        setLoading(true)\n        const data = await api.getPatients()\n        setPatients(data)\n        setFilteredPatients(data)\n\n        // Calculate patient statistics\n        const totalPatients = data.length\n        const totalAppointments = data.reduce((sum, patient) => sum + (patient.total_appointments || 0), 0)\n        const newThisMonth = data.filter(patient => {\n          const createdDate = new Date(patient.created_at)\n          const now = new Date()\n          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)\n          return createdDate >= monthStart\n        }).length\n        const activePatients = data.filter(patient => {\n          if (!patient.last_visit) return false\n          const lastVisit = new Date(patient.last_visit)\n          const threeMonthsAgo = new Date()\n          threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)\n          return lastVisit >= threeMonthsAgo\n        }).length\n\n        setStats({\n          total: totalPatients,\n          newThisMonth,\n          activePatients,\n          totalAppointments\n        })\n      } catch (error) {\n        console.error('Error loading patients:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadPatients()\n  }, [])\n\n  useEffect(() => {\n    let filtered = patients\n\n    if (searchQuery) {\n      filtered = filtered.filter(patient =>\n        patient.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        patient.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        patient.phone?.includes(searchQuery)\n      )\n    }\n\n    setFilteredPatients(filtered)\n  }, [patients, searchQuery])\n\n  const handleRefresh = async () => {\n    try {\n      setLoading(true)\n      const data = await api.getPatients()\n      setPatients(data)\n      setFilteredPatients(data)\n\n      // Recalculate patient statistics\n      const totalPatients = data.length\n      const totalAppointments = data.reduce((sum, patient) => sum + (patient.total_appointments || 0), 0)\n      const newThisMonth = data.filter(patient => {\n        const createdDate = new Date(patient.created_at)\n        const now = new Date()\n        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)\n        return createdDate >= monthStart\n      }).length\n      const activePatients = data.filter(patient => {\n        if (!patient.last_visit) return false\n        const lastVisit = new Date(patient.last_visit)\n        const threeMonthsAgo = new Date()\n        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)\n        return lastVisit >= threeMonthsAgo\n      }).length\n\n      setStats({\n        total: totalPatients,\n        newThisMonth,\n        activePatients,\n        totalAppointments\n      })\n    } catch (error) {\n      console.error('Error refreshing patients:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleViewDetails = (patient: Patient) => {\n    setSelectedPatient(patient)\n    setDetailsModalOpen(true)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold tracking-tight\">Patients</h1>\n            <p className=\"text-muted-foreground\">Manage patient records and information</p>\n          </div>\n        </div>\n        <div className=\"grid gap-4\">\n          {[...Array(5)].map((_, i) => (\n            <div key={i} className=\"h-16 bg-muted animate-pulse rounded-lg\" />\n          ))}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Patients</h1>\n          <p className=\"text-muted-foreground\">\n            Manage patient records and information\n          </p>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button onClick={handleRefresh} variant=\"outline\" size=\"sm\">\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            Refresh\n          </Button>\n          <Button>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Add Patient\n          </Button>\n        </div>\n      </div>\n\n      {/* Patient Statistics */}\n      {stats && (\n        <StatsGrid>\n          <StatsCard\n            title=\"Total Patients\"\n            value={stats.total}\n            icon={Users}\n            description=\"All registered patients\"\n          />\n          <StatsCard\n            title=\"New This Month\"\n            value={stats.newThisMonth}\n            icon={User}\n            description=\"Patients registered this month\"\n          />\n          <StatsCard\n            title=\"Active Patients\"\n            value={stats.activePatients}\n            icon={Users}\n            description=\"Visited in last 3 months\"\n          />\n          <StatsCard\n            title=\"Total Appointments\"\n            value={stats.totalAppointments}\n            icon={Calendar}\n            description=\"All appointments booked\"\n          />\n        </StatsGrid>\n      )}\n\n      {/* Search */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">Search Patients</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n            <Input\n              placeholder=\"Search by name, email, or phone...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Patients Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Patients ({filteredPatients.length})</CardTitle>\n          <CardDescription>\n            All registered patients and their information\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {filteredPatients.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Users className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium\">No patients found</h3>\n              <p className=\"text-muted-foreground\">\n                {searchQuery \n                  ? 'Try adjusting your search query' \n                  : 'No patients have been registered yet'}\n              </p>\n            </div>\n          ) : (\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Patient</TableHead>\n                    <TableHead>Contact</TableHead>\n                    <TableHead>Age</TableHead>\n                    <TableHead>Last Visit</TableHead>\n                    <TableHead>Total Appointments</TableHead>\n                    <TableHead className=\"text-right\">Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {filteredPatients.map((patient) => (\n                    <TableRow key={patient.id}>\n                      <TableCell>\n                        <div className=\"flex items-center gap-3\">\n                          <Avatar className=\"h-10 w-10\">\n                            <AvatarImage src={patient.avatar_url || undefined} />\n                            <AvatarFallback className=\"bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300\">\n                              {getInitials(patient.full_name || 'Unknown')}\n                            </AvatarFallback>\n                          </Avatar>\n                          <div>\n                            <div className=\"font-medium\">{patient.full_name}</div>\n                            <div className=\"text-sm text-muted-foreground\">\n                              Patient #{patients.findIndex(p => p.id === patient.id) + 1}\n                            </div>\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"space-y-1\">\n                          <div className=\"flex items-center gap-2 text-sm\">\n                            <Mail className=\"h-3 w-3 text-muted-foreground\" />\n                            <span>{patient.email}</span>\n                          </div>\n                          <div className=\"flex items-center gap-2 text-sm\">\n                            <Phone className=\"h-3 w-3 text-muted-foreground\" />\n                            <span>{patient.phone}</span>\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {patient.date_of_birth \n                              ? `${calculateAge(patient.date_of_birth)} years`\n                              : 'N/A'\n                            }\n                          </span>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          {patient.last_visit \n                            ? formatDate(patient.last_visit)\n                            : 'Never'\n                          }\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant=\"secondary\">\n                          {patient.total_appointments || 0} appointments\n                        </Badge>\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        <div className=\"flex items-center justify-end space-x-2\">\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleViewDetails(patient)}\n                          >\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"sm\" className=\"text-red-600 hover:text-red-700\">\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Patient Details Modal */}\n      <PatientDetailsModal\n        patient={selectedPatient}\n        open={detailsModalOpen}\n        onOpenChange={setDetailsModalOpen}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAEA;AAEA;AAlCA;;;;;;;;;;;;;AAoCA,oBAAoB;AACpB,MAAM,cAAc,CAAC;IACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEA,MAAM,aAAa,CAAC;IAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEA,MAAM,eAAe,CAAC;IACpB,MAAM,QAAQ,IAAI;IAClB,MAAM,YAAY,IAAI,KAAK;IAC3B,IAAI,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;IACrD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;IAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;QAC/E;IACF;IAEA,OAAO;AACT;AASO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iNAAQ,EAAY,EAAE;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAsB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAiB;IACvE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iNAAQ,EAAC;IAEzD,IAAA,kNAAS,EAAC;QACR,eAAe;YACb,IAAI;gBACF,WAAW;gBACX,MAAM,OAAO,MAAM,kIAAG,CAAC,WAAW;gBAClC,YAAY;gBACZ,oBAAoB;gBAEpB,+BAA+B;gBAC/B,MAAM,gBAAgB,KAAK,MAAM;gBACjC,MAAM,oBAAoB,KAAK,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,kBAAkB,IAAI,CAAC,GAAG;gBACjG,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;oBAC/B,MAAM,cAAc,IAAI,KAAK,QAAQ,UAAU;oBAC/C,MAAM,MAAM,IAAI;oBAChB,MAAM,aAAa,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;oBAC/D,OAAO,eAAe;gBACxB,GAAG,MAAM;gBACT,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAA;oBACjC,IAAI,CAAC,QAAQ,UAAU,EAAE,OAAO;oBAChC,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;oBAC7C,MAAM,iBAAiB,IAAI;oBAC3B,eAAe,QAAQ,CAAC,eAAe,QAAQ,KAAK;oBACpD,OAAO,aAAa;gBACtB,GAAG,MAAM;gBAET,SAAS;oBACP,OAAO;oBACP;oBACA;oBACA;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAA,kNAAS,EAAC;QACR,IAAI,WAAW;QAEf,IAAI,aAAa;YACf,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,SAAS,EAAE,cAAc,SAAS,YAAY,WAAW,OACjE,QAAQ,KAAK,EAAE,cAAc,SAAS,YAAY,WAAW,OAC7D,QAAQ,KAAK,EAAE,SAAS;QAE5B;QAEA,oBAAoB;IACtB,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,kIAAG,CAAC,WAAW;YAClC,YAAY;YACZ,oBAAoB;YAEpB,iCAAiC;YACjC,MAAM,gBAAgB,KAAK,MAAM;YACjC,MAAM,oBAAoB,KAAK,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,kBAAkB,IAAI,CAAC,GAAG;YACjG,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;gBAC/B,MAAM,cAAc,IAAI,KAAK,QAAQ,UAAU;gBAC/C,MAAM,MAAM,IAAI;gBAChB,MAAM,aAAa,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;gBAC/D,OAAO,eAAe;YACxB,GAAG,MAAM;YACT,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAA;gBACjC,IAAI,CAAC,QAAQ,UAAU,EAAE,OAAO;gBAChC,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;gBAC7C,MAAM,iBAAiB,IAAI;gBAC3B,eAAe,QAAQ,CAAC,eAAe,QAAQ,KAAK;gBACpD,OAAO,aAAa;YACtB,GAAG,MAAM;YAET,SAAS;gBACP,OAAO;gBACP;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAGzC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;IAKpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4IAAM;gCAAC,SAAS;gCAAe,SAAQ;gCAAU,MAAK;;kDACrD,8OAAC,6NAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC,4IAAM;;kDACL,8OAAC,0MAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAOtC,uBACC,8OAAC,sJAAS;;kCACR,8OAAC,sJAAS;wBACR,OAAM;wBACN,OAAO,MAAM,KAAK;wBAClB,MAAM,6MAAK;wBACX,aAAY;;;;;;kCAEd,8OAAC,sJAAS;wBACR,OAAM;wBACN,OAAO,MAAM,YAAY;wBACzB,MAAM,0MAAI;wBACV,aAAY;;;;;;kCAEd,8OAAC,sJAAS;wBACR,OAAM;wBACN,OAAO,MAAM,cAAc;wBAC3B,MAAM,6MAAK;wBACX,aAAY;;;;;;kCAEd,8OAAC,sJAAS;wBACR,OAAM;wBACN,OAAO,MAAM,iBAAiB;wBAC9B,MAAM,sNAAQ;wBACd,aAAY;;;;;;;;;;;;0BAMlB,8OAAC,wIAAI;;kCACH,8OAAC,8IAAU;kCACT,cAAA,8OAAC,6IAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,+IAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,0IAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC,wIAAI;;kCACH,8OAAC,8IAAU;;0CACT,8OAAC,6IAAS;;oCAAC;oCAAW,iBAAiB,MAAM;oCAAC;;;;;;;0CAC9C,8OAAC,mJAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,+IAAW;kCACT,iBAAiB,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6MAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAE,WAAU;8CACV,cACG,oCACA;;;;;;;;;;;iDAIR,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0IAAK;;kDACJ,8OAAC,gJAAW;kDACV,cAAA,8OAAC,6IAAQ;;8DACP,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;8DAAC;;;;;;8DACX,8OAAC,8IAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,8OAAC,8IAAS;kDACP,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,6IAAQ;;kEACP,8OAAC,8IAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4IAAM;oEAAC,WAAU;;sFAChB,8OAAC,iJAAW;4EAAC,KAAK,QAAQ,UAAU,IAAI;;;;;;sFACxC,8OAAC,oJAAc;4EAAC,WAAU;sFACvB,YAAY,QAAQ,SAAS,IAAI;;;;;;;;;;;;8EAGtC,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAe,QAAQ,SAAS;;;;;;sFAC/C,8OAAC;4EAAI,WAAU;;gFAAgC;gFACnC,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kEAKjE,8OAAC,8IAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;sFAAM,QAAQ,KAAK;;;;;;;;;;;;8EAEtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,6MAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;sFAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;kEAI1B,8OAAC,8IAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sNAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;8EACE,QAAQ,aAAa,GAClB,GAAG,aAAa,QAAQ,aAAa,EAAE,MAAM,CAAC,GAC9C;;;;;;;;;;;;;;;;;kEAKV,8OAAC,8IAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,UAAU,GACf,WAAW,QAAQ,UAAU,IAC7B;;;;;;;;;;;kEAIR,8OAAC,8IAAS;kEACR,cAAA,8OAAC,0IAAK;4DAAC,SAAQ;;gEACZ,QAAQ,kBAAkB,IAAI;gEAAE;;;;;;;;;;;;kEAGrC,8OAAC,8IAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4IAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,kBAAkB;8EAEjC,cAAA,8OAAC,uMAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,8OAAC,4IAAM;oEAAC,SAAQ;oEAAQ,MAAK;8EAC3B,cAAA,8OAAC,mNAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,8OAAC,4IAAM;oEAAC,SAAQ;oEAAQ,MAAK;oEAAK,WAAU;8EAC1C,cAAA,8OAAC,oNAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAlEX,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgFvC,8OAAC,oLAAmB;gBAClB,SAAS;gBACT,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}]}