"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"30f21029fc0d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGV0cmFcXE9uZURyaXZlXFxEZXNrdG9wXFxEZW50aXN0IHdlYnNpdGVcXHBhdGllbnQtZmFjaW5nXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzBmMjEwMjlmYzBkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/appointment-form.tsx":
/*!*****************************************!*\
  !*** ./components/appointment-form.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentForm: () => (/* binding */ AppointmentForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/hydration-safe-input */ \"(app-pages-browser)/./components/ui/hydration-safe-input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AppointmentForm(param) {\n    let { onSuccess } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Basic Information\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        // Appointment Details\n        service: \"\",\n        preferred_date: \"\",\n        preferred_time: \"\",\n        message: \"\",\n        // Medical History\n        medical_history: \"\",\n        allergies: \"\",\n        current_medications: \"\",\n        previous_dental_work: \"\",\n        dental_concerns: \"\",\n        // Emergency Contact\n        emergency_contact_name: \"\",\n        emergency_contact_phone: \"\",\n        emergency_contact_relationship: \"\",\n        // Insurance Information (Optional)\n        has_insurance: \"\",\n        insurance_provider: \"\",\n        insurance_policy_number: \"\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const [availableTimeSlots, setAvailableTimeSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingSlots, setLoadingSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch available time slots when date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (formData.preferred_date) {\n                fetchAvailableSlots(formData.preferred_date);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        formData.preferred_date\n    ]);\n    const fetchAvailableSlots = async (date)=>{\n        setLoadingSlots(true);\n        try {\n            const response = await fetch(\"/api/availability?date=\".concat(date));\n            const data = await response.json();\n            if (data.available && data.timeSlots) {\n                // Transform time slots to include availability information\n                const slots = data.timeSlots.filter((slot)=>{\n                    const currentBookings = slot.current_appointments || 0;\n                    return currentBookings < slot.max_appointments;\n                }).map((slot)=>({\n                        time: slot.time,\n                        maxAppointments: slot.max_appointments,\n                        currentAppointments: slot.current_appointments || 0,\n                        availableSpots: slot.max_appointments - (slot.current_appointments || 0)\n                    }));\n                setAvailableTimeSlots(slots);\n            } else {\n                setAvailableTimeSlots([]);\n            }\n        } catch (error) {\n            console.error('Error fetching available slots:', error);\n            setAvailableTimeSlots([]);\n        } finally{\n            setLoadingSlots(false);\n        }\n    };\n    const services = [\n        \"✨ The Signature Glow\",\n        \"⚡ Express Refresh\",\n        \"💎 Complete Smile Makeover\",\n        \"🦷 Clear Aligners (Premium)\",\n        \"🔧 Root Canal Treatment\",\n        \"👑 Dental Crowns\",\n        \"🧽 Professional Cleaning\",\n        \"🦷 Dental Implants\",\n        \"Other (Please specify in message)\"\n    ];\n    const timeSlots = [\n        \"9:00 AM\",\n        \"10:00 AM\",\n        \"11:00 AM\",\n        \"12:00 PM\",\n        \"2:00 PM\",\n        \"3:00 PM\",\n        \"4:00 PM\",\n        \"5:00 PM\",\n        \"6:00 PM\",\n        \"7:00 PM\",\n        \"8:00 PM\"\n    ];\n    const handleChange = (e)=>{\n        const { id, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [id]: value\n            }));\n    };\n    const handleSelectChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.name && formData.email && formData.phone && formData.date_of_birth;\n            case 2:\n                return formData.service && formData.preferred_date && formData.preferred_time;\n            case 3:\n                return formData.emergency_contact_name && formData.emergency_contact_phone;\n            case 4:\n                return true // Optional step\n                ;\n            default:\n                return false;\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            const response = await fetch('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setSubmitStatus('success');\n                setFormData({\n                    // Basic Information\n                    name: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    date_of_birth: \"\",\n                    address: \"\",\n                    // Appointment Details\n                    service: \"\",\n                    preferred_date: \"\",\n                    preferred_time: \"\",\n                    message: \"\",\n                    // Medical History\n                    medical_history: \"\",\n                    allergies: \"\",\n                    current_medications: \"\",\n                    previous_dental_work: \"\",\n                    dental_concerns: \"\",\n                    // Emergency Contact\n                    emergency_contact_name: \"\",\n                    emergency_contact_phone: \"\",\n                    emergency_contact_relationship: \"\",\n                    // Insurance Information (Optional)\n                    has_insurance: \"\",\n                    insurance_provider: \"\",\n                    insurance_policy_number: \"\"\n                });\n                setCurrentStep(1); // Reset to first step\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                setSubmitStatus('error');\n                console.error('Appointment booking error:', result.message);\n            }\n        } catch (error) {\n            setSubmitStatus('error');\n            console.error('Network error:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Get today's date in YYYY-MM-DD format for min date\n    const today = new Date().toISOString().split('T')[0];\n    const stepTitles = [\n        \"Personal Information\",\n        \"Appointment Details\",\n        \"Medical History & Emergency Contact\",\n        \"Insurance Information (Optional)\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: [\n                                    \"Step \",\n                                    currentStep,\n                                    \" of \",\n                                    totalSteps,\n                                    \": \",\n                                    stepTitles[currentStep - 1]\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    Math.round(currentStep / totalSteps * 100),\n                                    \"% Complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#6366F1] to-[#F59E0B] h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-green-50 border border-green-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-800 font-medium\",\n                    children: \"\\uD83C\\uDF89 Appointment booked successfully! We will call you within 2 hours to confirm your appointment.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this),\n            submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border border-red-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Sorry, there was an error booking your appointment. Please try again or call us directly at +91-11-41234567.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this),\n            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"name\",\n                                        className: \"text-foreground\",\n                                        children: \"Full Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"name\",\n                                        type: \"text\",\n                                        placeholder: \"Your Full Name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"email\",\n                                        className: \"text-foreground\",\n                                        children: \"Email *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        placeholder: \"<EMAIL>\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"phone\",\n                                        className: \"text-foreground\",\n                                        children: \"Phone Number *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"phone\",\n                                        type: \"tel\",\n                                        placeholder: \"+91-XXXXXXXXXX\",\n                                        value: formData.phone,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"date_of_birth\",\n                                        className: \"text-foreground\",\n                                        children: \"Date of Birth *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"date_of_birth\",\n                                        type: \"date\",\n                                        value: formData.date_of_birth,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"address\",\n                                className: \"text-foreground\",\n                                children: \"Address\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"address\",\n                                placeholder: \"Your complete address\",\n                                value: formData.address,\n                                onChange: handleChange,\n                                className: \"mt-1\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this),\n            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"service\",\n                                className: \"text-foreground\",\n                                children: \"Service Required *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                onValueChange: (value)=>handleSelectChange('service', value),\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Select a service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: service,\n                                                children: service\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_date\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Date *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"preferred_date\",\n                                        type: \"date\",\n                                        min: today,\n                                        value: formData.preferred_date,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_time\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Time *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                        onValueChange: (value)=>handleSelectChange('preferred_time', value),\n                                        disabled: isSubmitting || loadingSlots || !formData.preferred_date,\n                                        value: formData.preferred_time,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: !formData.preferred_date ? \"Please select a date first\" : loadingSlots ? \"Loading available times...\" : availableTimeSlots.length === 0 ? \"No availability for this date\" : \"Select time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                children: availableTimeSlots.map((time)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: time,\n                                                        children: time\n                                                    }, time, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.preferred_date && !loadingSlots && availableTimeSlots.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600 mt-1\",\n                                        children: \"No availability for this date. Please select a different date.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"message\",\n                                className: \"text-foreground\",\n                                children: \"Additional Message\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"message\",\n                                placeholder: \"Any specific concerns or requirements? (Optional)\",\n                                value: formData.message,\n                                onChange: handleChange,\n                                className: \"mt-1 min-h-[100px]\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, this),\n            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Medical History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"medical_history\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"medical_history\",\n                                        placeholder: \"Please list any medical conditions, surgeries, or ongoing treatments\",\n                                        value: formData.medical_history,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"allergies\",\n                                        className: \"text-foreground\",\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"allergies\",\n                                        placeholder: \"Please list any allergies (medications, foods, materials, etc.)\",\n                                        value: formData.allergies,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"current_medications\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"current_medications\",\n                                        placeholder: \"Please list all medications you are currently taking\",\n                                        value: formData.current_medications,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"previous_dental_work\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Dental Work\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"previous_dental_work\",\n                                        placeholder: \"Please describe any previous dental treatments or procedures\",\n                                        value: formData.previous_dental_work,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"dental_concerns\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Dental Concerns\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"dental_concerns\",\n                                        placeholder: \"Please describe any current dental pain, concerns, or symptoms\",\n                                        value: formData.dental_concerns,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Emergency Contact *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_name\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_name\",\n                                                type: \"text\",\n                                                placeholder: \"Emergency contact full name\",\n                                                value: formData.emergency_contact_name,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_phone\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Phone *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_phone\",\n                                                type: \"tel\",\n                                                placeholder: \"+91-XXXXXXXXXX\",\n                                                value: formData.emergency_contact_phone,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"emergency_contact_relationship\",\n                                        className: \"text-foreground\",\n                                        children: \"Relationship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"emergency_contact_relationship\",\n                                        type: \"text\",\n                                        placeholder: \"e.g., Spouse, Parent, Sibling, Friend\",\n                                        value: formData.emergency_contact_relationship,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 441,\n                columnNumber: 9\n            }, this),\n            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-foreground\",\n                            children: \"Insurance Information (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"This information helps us process your treatment more efficiently, but it's completely optional.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"has_insurance\",\n                                    className: \"text-foreground\",\n                                    children: \"Do you have dental insurance?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: (value)=>handleSelectChange('has_insurance', value),\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                placeholder: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"yes\",\n                                                    children: \"Yes, I have dental insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"no\",\n                                                    children: \"No, I don't have insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"unsure\",\n                                                    children: \"I'm not sure\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 13\n                        }, this),\n                        formData.has_insurance === 'yes' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_provider\",\n                                            className: \"text-foreground\",\n                                            children: \"Insurance Provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_provider\",\n                                            type: \"text\",\n                                            placeholder: \"e.g., Star Health, HDFC ERGO, etc.\",\n                                            value: formData.insurance_provider,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_policy_number\",\n                                            className: \"text-foreground\",\n                                            children: \"Policy Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_policy_number\",\n                                            type: \"text\",\n                                            placeholder: \"Your insurance policy number\",\n                                            value: formData.insurance_policy_number,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 573,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6\",\n                children: [\n                    currentStep > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: prevStep,\n                        disabled: isSubmitting,\n                        className: \"px-6\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto\",\n                        children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            onClick: nextStep,\n                            disabled: !isStepValid(currentStep) || isSubmitting,\n                            className: \"px-6 bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white\",\n                            children: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            disabled: isSubmitting || !isStepValid(currentStep),\n                            className: \"px-8 rounded-2xl bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white font-semibold py-3 text-lg shadow-lg hover:from-[#5a5ee0] hover:to-[#e08d0a] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Booking Appointment...\"\n                                ]\n                            }, void 0, true) : 'Book Appointment'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 634,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-muted-foreground text-center\",\n                children: [\n                    \"* We will call you within 2 hours to confirm your appointment. For urgent needs, call us directly at\",\n                    ' ',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+911141234567\",\n                        className: \"text-primary hover:underline\",\n                        children: \"+91-11-41234567\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 676,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentForm, \"Yzw6WW5nxYcVlkDjUXzjiEoj0ok=\");\n_c = AppointmentForm;\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/appointment-form.tsx\n"));

/***/ })

});