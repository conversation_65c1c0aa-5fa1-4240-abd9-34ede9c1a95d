"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/book/page",{

/***/ "(app-pages-browser)/./components/appointment-form.tsx":
/*!*****************************************!*\
  !*** ./components/appointment-form.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentForm: () => (/* binding */ AppointmentForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/hydration-safe-input */ \"(app-pages-browser)/./components/ui/hydration-safe-input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AppointmentForm(param) {\n    let { onSuccess } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Basic Information\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        date_of_birth: \"\",\n        address: \"\",\n        // Appointment Details\n        service: \"\",\n        preferred_date: \"\",\n        preferred_time: \"\",\n        message: \"\",\n        // Medical History\n        medical_history: \"\",\n        allergies: \"\",\n        current_medications: \"\",\n        previous_dental_work: \"\",\n        dental_concerns: \"\",\n        // Emergency Contact\n        emergency_contact_name: \"\",\n        emergency_contact_phone: \"\",\n        emergency_contact_relationship: \"\",\n        // Insurance Information (Optional)\n        has_insurance: \"\",\n        insurance_provider: \"\",\n        insurance_policy_number: \"\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const services = [\n        \"✨ The Signature Glow\",\n        \"⚡ Express Refresh\",\n        \"💎 Complete Smile Makeover\",\n        \"🦷 Clear Aligners (Premium)\",\n        \"🔧 Root Canal Treatment\",\n        \"👑 Dental Crowns\",\n        \"🧽 Professional Cleaning\",\n        \"🦷 Dental Implants\",\n        \"Other (Please specify in message)\"\n    ];\n    const timeSlots = [\n        \"9:00 AM\",\n        \"10:00 AM\",\n        \"11:00 AM\",\n        \"12:00 PM\",\n        \"2:00 PM\",\n        \"3:00 PM\",\n        \"4:00 PM\",\n        \"5:00 PM\",\n        \"6:00 PM\",\n        \"7:00 PM\",\n        \"8:00 PM\"\n    ];\n    const handleChange = (e)=>{\n        const { id, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [id]: value\n            }));\n    };\n    const handleSelectChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.name && formData.email && formData.phone && formData.date_of_birth;\n            case 2:\n                return formData.service && formData.preferred_date && formData.preferred_time;\n            case 3:\n                return formData.emergency_contact_name && formData.emergency_contact_phone;\n            case 4:\n                return true // Optional step\n                ;\n            default:\n                return false;\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            const response = await fetch('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setSubmitStatus('success');\n                setFormData({\n                    // Basic Information\n                    name: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    date_of_birth: \"\",\n                    address: \"\",\n                    // Appointment Details\n                    service: \"\",\n                    preferred_date: \"\",\n                    preferred_time: \"\",\n                    message: \"\",\n                    // Medical History\n                    medical_history: \"\",\n                    allergies: \"\",\n                    current_medications: \"\",\n                    previous_dental_work: \"\",\n                    dental_concerns: \"\",\n                    // Emergency Contact\n                    emergency_contact_name: \"\",\n                    emergency_contact_phone: \"\",\n                    emergency_contact_relationship: \"\",\n                    // Insurance Information (Optional)\n                    has_insurance: \"\",\n                    insurance_provider: \"\",\n                    insurance_policy_number: \"\"\n                });\n                setCurrentStep(1); // Reset to first step\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                setSubmitStatus('error');\n                console.error('Appointment booking error:', result.message);\n            }\n        } catch (error) {\n            setSubmitStatus('error');\n            console.error('Network error:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Get today's date in YYYY-MM-DD format for min date\n    const today = new Date().toISOString().split('T')[0];\n    const stepTitles = [\n        \"Personal Information\",\n        \"Appointment Details\",\n        \"Medical History & Emergency Contact\",\n        \"Insurance Information (Optional)\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: [\n                                    \"Step \",\n                                    currentStep,\n                                    \" of \",\n                                    totalSteps,\n                                    \": \",\n                                    stepTitles[currentStep - 1]\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    Math.round(currentStep / totalSteps * 100),\n                                    \"% Complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#6366F1] to-[#F59E0B] h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-green-50 border border-green-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-800 font-medium\",\n                    children: \"\\uD83C\\uDF89 Appointment booked successfully! We will call you within 2 hours to confirm your appointment.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border border-red-200 rounded-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Sorry, there was an error booking your appointment. Please try again or call us directly at +91-11-41234567.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this),\n            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"name\",\n                                        className: \"text-foreground\",\n                                        children: \"Full Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"name\",\n                                        type: \"text\",\n                                        placeholder: \"Your Full Name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"email\",\n                                        className: \"text-foreground\",\n                                        children: \"Email *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        placeholder: \"<EMAIL>\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"phone\",\n                                        className: \"text-foreground\",\n                                        children: \"Phone Number *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"phone\",\n                                        type: \"tel\",\n                                        placeholder: \"+91-XXXXXXXXXX\",\n                                        value: formData.phone,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"date_of_birth\",\n                                        className: \"text-foreground\",\n                                        children: \"Date of Birth *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"date_of_birth\",\n                                        type: \"date\",\n                                        value: formData.date_of_birth,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"address\",\n                                className: \"text-foreground\",\n                                children: \"Address\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"address\",\n                                placeholder: \"Your complete address\",\n                                value: formData.address,\n                                onChange: handleChange,\n                                className: \"mt-1\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this),\n            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"service\",\n                                className: \"text-foreground\",\n                                children: \"Service Required *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                onValueChange: (value)=>handleSelectChange('service', value),\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Select a service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: service,\n                                                children: service\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"preferred_date\",\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Date *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"preferred_date\",\n                                        type: \"date\",\n                                        min: today,\n                                        value: formData.preferred_date,\n                                        onChange: handleChange,\n                                        required: true,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Preferred Time *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvailabilityChecker, {\n                                            selectedDate: formData.preferred_date,\n                                            selectedTime: formData.preferred_time,\n                                            onTimeSelect: (time)=>handleSelectChange('preferred_time', time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"message\",\n                                className: \"text-foreground\",\n                                children: \"Additional Message\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                id: \"message\",\n                                placeholder: \"Any specific concerns or requirements? (Optional)\",\n                                value: formData.message,\n                                onChange: handleChange,\n                                className: \"mt-1 min-h-[100px]\",\n                                disabled: isSubmitting\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, this),\n            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Medical History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"medical_history\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"medical_history\",\n                                        placeholder: \"Please list any medical conditions, surgeries, or ongoing treatments\",\n                                        value: formData.medical_history,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"allergies\",\n                                        className: \"text-foreground\",\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"allergies\",\n                                        placeholder: \"Please list any allergies (medications, foods, materials, etc.)\",\n                                        value: formData.allergies,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"current_medications\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"current_medications\",\n                                        placeholder: \"Please list all medications you are currently taking\",\n                                        value: formData.current_medications,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"previous_dental_work\",\n                                        className: \"text-foreground\",\n                                        children: \"Previous Dental Work\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"previous_dental_work\",\n                                        placeholder: \"Please describe any previous dental treatments or procedures\",\n                                        value: formData.previous_dental_work,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"dental_concerns\",\n                                        className: \"text-foreground\",\n                                        children: \"Current Dental Concerns\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"dental_concerns\",\n                                        placeholder: \"Please describe any current dental pain, concerns, or symptoms\",\n                                        value: formData.dental_concerns,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-foreground\",\n                                children: \"Emergency Contact *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_name\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_name\",\n                                                type: \"text\",\n                                                placeholder: \"Emergency contact full name\",\n                                                value: formData.emergency_contact_name,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"emergency_contact_phone\",\n                                                className: \"text-foreground\",\n                                                children: \"Contact Phone *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                                id: \"emergency_contact_phone\",\n                                                type: \"tel\",\n                                                placeholder: \"+91-XXXXXXXXXX\",\n                                                value: formData.emergency_contact_phone,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"mt-1\",\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"emergency_contact_relationship\",\n                                        className: \"text-foreground\",\n                                        children: \"Relationship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                        id: \"emergency_contact_relationship\",\n                                        type: \"text\",\n                                        placeholder: \"e.g., Spouse, Parent, Sibling, Friend\",\n                                        value: formData.emergency_contact_relationship,\n                                        onChange: handleChange,\n                                        className: \"mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 376,\n                columnNumber: 9\n            }, this),\n            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-foreground\",\n                            children: \"Insurance Information (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"This information helps us process your treatment more efficiently, but it's completely optional.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"has_insurance\",\n                                    className: \"text-foreground\",\n                                    children: \"Do you have dental insurance?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: (value)=>handleSelectChange('has_insurance', value),\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                placeholder: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"yes\",\n                                                    children: \"Yes, I have dental insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"no\",\n                                                    children: \"No, I don't have insurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"unsure\",\n                                                    children: \"I'm not sure\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, this),\n                        formData.has_insurance === 'yes' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_provider\",\n                                            className: \"text-foreground\",\n                                            children: \"Insurance Provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_provider\",\n                                            type: \"text\",\n                                            placeholder: \"e.g., Star Health, HDFC ERGO, etc.\",\n                                            value: formData.insurance_provider,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"insurance_policy_number\",\n                                            className: \"text-foreground\",\n                                            children: \"Policy Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hydration_safe_input__WEBPACK_IMPORTED_MODULE_3__.HydrationSafeInput, {\n                                            id: \"insurance_policy_number\",\n                                            type: \"text\",\n                                            placeholder: \"Your insurance policy number\",\n                                            value: formData.insurance_policy_number,\n                                            onChange: handleChange,\n                                            className: \"mt-1\",\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6\",\n                children: [\n                    currentStep > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: prevStep,\n                        disabled: isSubmitting,\n                        className: \"px-6\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-auto\",\n                        children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            onClick: nextStep,\n                            disabled: !isStepValid(currentStep) || isSubmitting,\n                            className: \"px-6 bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white\",\n                            children: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            disabled: isSubmitting || !isStepValid(currentStep),\n                            className: \"px-8 rounded-2xl bg-gradient-to-r from-[#6366F1] to-[#F59E0B] text-white font-semibold py-3 text-lg shadow-lg hover:from-[#5a5ee0] hover:to-[#e08d0a] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Booking Appointment...\"\n                                ]\n                            }, void 0, true) : 'Book Appointment'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 569,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-muted-foreground text-center\",\n                children: [\n                    \"* We will call you within 2 hours to confirm your appointment. For urgent needs, call us directly at\",\n                    ' ',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+911141234567\",\n                        className: \"text-primary hover:underline\",\n                        children: \"+91-11-41234567\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n                lineNumber: 611,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dentist website\\\\patient-facing\\\\components\\\\appointment-form.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentForm, \"2hjE+aafflVHPHyBXwLKLjBd4Nw=\");\n_c = AppointmentForm;\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/appointment-form.tsx\n"));

/***/ })

});