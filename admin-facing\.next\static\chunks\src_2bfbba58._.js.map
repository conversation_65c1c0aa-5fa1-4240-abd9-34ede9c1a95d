{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number) {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(amount / 100) // Convert from cents to rupees\n}\n\nexport function formatDate(date: string | Date) {\n  return new Date(date).toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  })\n}\n\nexport function getRelativeTime(dateString: string) {\n  const date = new Date(dateString)\n  const now = new Date()\n  const diffInMs = now.getTime() - date.getTime()\n  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n\n  if (diffInDays === 0) {\n    return 'Today'\n  } else if (diffInDays === 1) {\n    return 'Yesterday'\n  } else if (diffInDays < 7) {\n    return `${diffInDays} days ago`\n  } else if (diffInDays < 30) {\n    const weeks = Math.floor(diffInDays / 7)\n    return `${weeks} week${weeks > 1 ? 's' : ''} ago`\n  } else if (diffInDays < 365) {\n    const months = Math.floor(diffInDays / 30)\n    return `${months} month${months > 1 ? 's' : ''} ago`\n  } else {\n    const years = Math.floor(diffInDays / 365)\n    return `${years} year${years > 1 ? 's' : ''} ago`\n  }\n}\n\nexport function getStatusColor(status: string) {\n  switch (status) {\n    case 'confirmed':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n    case 'pending':\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'\n    case 'completed':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n    case 'cancelled':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n    case 'in_progress':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n  }\n}\n\nexport function getPriorityColor(priority: string) {\n  switch (priority) {\n    case 'urgent':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n    case 'high':\n      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'\n    case 'medium':\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'\n    case 'low':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n  }\n}\n\nexport function formatTime(timeString: string) {\n  return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-IN', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS,KAAK,+BAA+B;;AACzD;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,gBAAgB,UAAkB;IAChD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;IAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE7D,IAAI,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,eAAe,GAAG;QAC3B,OAAO;IACT,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,AAAC,GAAa,OAAX,YAAW;IACvB,OAAO,IAAI,aAAa,IAAI;QAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;IAC9C,OAAO,IAAI,aAAa,KAAK;QAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;QACvC,OAAO,AAAC,GAAiB,OAAf,QAAO,UAA8B,OAAtB,SAAS,IAAI,MAAM,IAAG;IACjD,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;IAC9C;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,AAAC,cAAwB,OAAX,aAAc,kBAAkB,CAAC,SAAS;QACtE,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport {\n  LayoutDashboard,\n  Calendar,\n  Users,\n  Stethoscope,\n  MessageSquare,\n  BarChart3,\n  Settings,\n  Bell,\n  FileText,\n  CreditCard,\n  UserCheck,\n  Clock,\n  Star,\n  Mail,\n  Shield,\n  Database,\n  Activity,\n  TrendingUp,\n  Zap\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/',\n    icon: LayoutDashboard,\n    description: 'Overview and analytics'\n  },\n  {\n    name: 'Appointments',\n    href: '/appointments',\n    icon: Calendar,\n    description: 'Manage appointments',\n    children: [\n      { name: 'All Appointments', href: '/appointments', icon: Calendar },\n      { name: 'Calendar View', href: '/appointments/calendar', icon: Clock },\n      { name: 'Pending Approval', href: '/appointments/pending', icon: UserCheck },\n    ]\n  },\n  {\n    name: 'Patients',\n    href: '/patients',\n    icon: Users,\n    description: 'Patient management',\n    children: [\n      { name: 'All Patients', href: '/patients', icon: Users },\n      { name: 'Medical Records', href: '/patients/records', icon: FileText },\n      { name: 'Patient History', href: '/patients/history', icon: Activity },\n    ]\n  },\n  {\n    name: 'Services',\n    href: '/services',\n    icon: Stethoscope,\n    description: 'Dental services',\n    children: [\n      { name: 'All Services', href: '/services', icon: Stethoscope },\n      { name: 'Pricing', href: '/services/pricing', icon: CreditCard },\n      { name: 'Categories', href: '/services/categories', icon: Database },\n    ]\n  },\n  {\n    name: 'Communications',\n    href: '/communications',\n    icon: MessageSquare,\n    description: 'Messages and notifications',\n    children: [\n      { name: 'Contact Forms', href: '/communications/contacts', icon: MessageSquare },\n      { name: 'Notifications', href: '/communications/notifications', icon: Bell },\n      { name: 'Newsletter', href: '/communications/newsletter', icon: Mail },\n      { name: 'Testimonials', href: '/communications/testimonials', icon: Star },\n    ]\n  },\n  {\n    name: 'Reports',\n    href: '/reports',\n    icon: BarChart3,\n    description: 'Analytics and reports',\n    children: [\n      { name: 'Revenue Analytics', href: '/reports/revenue', icon: TrendingUp },\n      { name: 'Appointment Trends', href: '/reports/appointments', icon: Activity },\n      { name: 'Patient Insights', href: '/reports/patients', icon: Users },\n      { name: 'Service Performance', href: '/reports/services', icon: Zap },\n    ]\n  },\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n    description: 'System configuration',\n    children: [\n      { name: 'General', href: '/settings', icon: Settings },\n      { name: 'Admin Users', href: '/settings/users', icon: Shield },\n      { name: 'Clinic Info', href: '/settings/clinic', icon: Database },\n      { name: 'Availability', href: '/settings/availability', icon: Clock },\n    ]\n  },\n]\n\ninterface SidebarProps {\n  className?: string\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <div className={cn('flex h-full w-64 flex-col bg-[#171717] border-r border-[#404040]', className)}>\n      {/* Logo */}\n      <div className=\"flex h-16 items-center border-b border-[#404040] px-6\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 shadow-lg\">\n            <Stethoscope className=\"h-5 w-5 text-white\" />\n          </div>\n          <div className=\"flex flex-col\">\n            <span className=\"text-base font-bold text-white\">DentalCare</span>\n            <span className=\"text-xs text-gray-400\">Admin Dashboard</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 space-y-2 p-4 overflow-y-auto scrollbar-thin\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href || pathname.startsWith(item.href + '/')\n\n          return (\n            <div key={item.name}>\n              <Link\n                href={item.href}\n                className={cn(\n                  'flex items-center gap-3 rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 hover:bg-[#2a2a2a] group',\n                  isActive\n                    ? 'bg-gradient-to-r from-orange-500/20 to-orange-600/20 text-orange-400 border-l-4 border-orange-500'\n                    : 'text-gray-300 hover:text-white'\n                )}\n              >\n                <item.icon className={cn(\n                  \"h-5 w-5 transition-colors\",\n                  isActive ? \"text-orange-400\" : \"text-gray-400 group-hover:text-white\"\n                )} />\n                <div className=\"flex flex-col\">\n                  <span className=\"font-medium\">{item.name}</span>\n                  <span className=\"text-xs text-gray-500 group-hover:text-gray-400\">\n                    {item.description}\n                  </span>\n                </div>\n              </Link>\n              \n              {/* Sub-navigation */}\n              {item.children && isActive && (\n                <div className=\"ml-8 mt-2 space-y-1\">\n                  {item.children.map((child) => {\n                    const isChildActive = pathname === child.href\n                    return (\n                      <Link\n                        key={child.name}\n                        href={child.href}\n                        className={cn(\n                          'flex items-center gap-2 rounded-lg px-3 py-2 text-xs transition-all duration-200 hover:bg-[#2a2a2a] group',\n                          isChildActive\n                            ? 'bg-orange-500/10 text-orange-300 border-l-2 border-orange-500'\n                            : 'text-gray-400 hover:text-gray-200'\n                        )}\n                      >\n                        <child.icon className={cn(\n                          \"h-3 w-3\",\n                          isChildActive ? \"text-orange-300\" : \"text-gray-500 group-hover:text-gray-300\"\n                        )} />\n                        {child.name}\n                      </Link>\n                    )\n                  })}\n                </div>\n              )}\n            </div>\n          )\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex items-center gap-3 rounded-lg bg-muted/50 p-3\">\n          <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium\">\n            A\n          </div>\n          <div className=\"flex flex-col\">\n            <span className=\"text-sm font-medium\">Admin User</span>\n            <span className=\"text-xs text-muted-foreground\"><EMAIL></span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AA2BA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,kPAAe;QACrB,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yNAAQ;QACd,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;gBAAiB,MAAM,yNAAQ;YAAC;YAClE;gBAAE,MAAM;gBAAiB,MAAM;gBAA0B,MAAM,gNAAK;YAAC;YACrE;gBAAE,MAAM;gBAAoB,MAAM;gBAAyB,MAAM,gOAAS;YAAC;SAC5E;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gNAAK;QACX,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAgB,MAAM;gBAAa,MAAM,gNAAK;YAAC;YACvD;gBAAE,MAAM;gBAAmB,MAAM;gBAAqB,MAAM,6NAAQ;YAAC;YACrE;gBAAE,MAAM;gBAAmB,MAAM;gBAAqB,MAAM,yNAAQ;YAAC;SACtE;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kOAAW;QACjB,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAgB,MAAM;gBAAa,MAAM,kOAAW;YAAC;YAC7D;gBAAE,MAAM;gBAAW,MAAM;gBAAqB,MAAM,mOAAU;YAAC;YAC/D;gBAAE,MAAM;gBAAc,MAAM;gBAAwB,MAAM,yNAAQ;YAAC;SACpE;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,4OAAa;QACnB,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAiB,MAAM;gBAA4B,MAAM,4OAAa;YAAC;YAC/E;gBAAE,MAAM;gBAAiB,MAAM;gBAAiC,MAAM,6MAAI;YAAC;YAC3E;gBAAE,MAAM;gBAAc,MAAM;gBAA8B,MAAM,6MAAI;YAAC;YACrE;gBAAE,MAAM;gBAAgB,MAAM;gBAAgC,MAAM,6MAAI;YAAC;SAC1E;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kOAAS;QACf,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAqB,MAAM;gBAAoB,MAAM,mOAAU;YAAC;YACxE;gBAAE,MAAM;gBAAsB,MAAM;gBAAyB,MAAM,yNAAQ;YAAC;YAC5E;gBAAE,MAAM;gBAAoB,MAAM;gBAAqB,MAAM,gNAAK;YAAC;YACnE;gBAAE,MAAM;gBAAuB,MAAM;gBAAqB,MAAM,0MAAG;YAAC;SACrE;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yNAAQ;QACd,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;gBAAa,MAAM,yNAAQ;YAAC;YACrD;gBAAE,MAAM;gBAAe,MAAM;gBAAmB,MAAM,mNAAM;YAAC;YAC7D;gBAAE,MAAM;gBAAe,MAAM;gBAAoB,MAAM,yNAAQ;YAAC;YAChE;gBAAE,MAAM;gBAAgB,MAAM;gBAA0B,MAAM,gNAAK;YAAC;SACrE;IACH;CACD;AAMM,SAAS,QAAQ,KAA2B;QAA3B,EAAE,SAAS,EAAgB,GAA3B;;IACtB,MAAM,WAAW,IAAA,oJAAW;IAE5B,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,oEAAoE;;0BAErF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,kOAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;8CACjD,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oBAE3E,qBACE,6LAAC;;0CACC,6LAAC,0KAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,IAAA,4HAAE,EACX,yHACA,WACI,sGACA;;kDAGN,6LAAC,KAAK,IAAI;wCAAC,WAAW,IAAA,4HAAE,EACtB,6BACA,WAAW,oBAAoB;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACxC,6LAAC;gDAAK,WAAU;0DACb,KAAK,WAAW;;;;;;;;;;;;;;;;;;4BAMtB,KAAK,QAAQ,IAAI,0BAChB,6LAAC;gCAAI,WAAU;0CACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;oCAClB,MAAM,gBAAgB,aAAa,MAAM,IAAI;oCAC7C,qBACE,6LAAC,0KAAI;wCAEH,MAAM,MAAM,IAAI;wCAChB,WAAW,IAAA,4HAAE,EACX,6GACA,gBACI,kEACA;;0DAGN,6LAAC,MAAM,IAAI;gDAAC,WAAW,IAAA,4HAAE,EACvB,WACA,gBAAgB,oBAAoB;;;;;;4CAErC,MAAM,IAAI;;uCAbN,MAAM,IAAI;;;;;gCAgBrB;;;;;;;uBA7CI,KAAK,IAAI;;;;;gBAkDvB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA+G;;;;;;sCAG9H,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5D;GA3FgB;;QACG,oJAAW;;;KADd", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,2KAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,2KAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // Additional props can be added here if needed\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAOA,MAAM,sBAAQ,2KAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,IAAA,4HAAE,EACX,qXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,IAAA,0KAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,uLAA0B;AAE/C,MAAM,sBAAsB,0LAA6B;AAEzD,MAAM,oBAAoB,wLAA2B;AAErD,MAAM,qBAAqB,yLAA4B;AAEvD,MAAM,kBAAkB,sLAAyB;AAEjD,MAAM,yBAAyB,6LAAgC;AAE/D,MAAM,uCAAyB,2KAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,6LAAC,6LAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yOAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,6LAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,2KAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,6LAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAChC,6LAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,2KAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,6LAAC,yLAA4B;kBAC3B,cAAA,6LAAC,0LAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,4HAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,0LAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,2KAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uLAA0B;QACzB,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,uLAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,2KAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,6LAAC,+LAAkC;QACjC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gMAAmC;8BAClC,cAAA,6LAAC,gNAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+LAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,2KAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,4LAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gMAAmC;8BAClC,cAAA,6LAAC,mNAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4LAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,2KAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,wLAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,wLAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,2KAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,4LAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,4HAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,4LAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      user_profiles: {\n        Row: {\n          id: string\n          created_at: string\n          updated_at: string\n          email: string\n          full_name: string | null\n          phone: string | null\n          date_of_birth: string | null\n          address: string | null\n          emergency_contact: string | null\n          medical_history: string | null\n          allergies: string | null\n          avatar_url: string | null\n        }\n        Insert: {\n          id: string\n          created_at?: string\n          updated_at?: string\n          email: string\n          full_name?: string | null\n          phone?: string | null\n          date_of_birth?: string | null\n          address?: string | null\n          emergency_contact?: string | null\n          medical_history?: string | null\n          allergies?: string | null\n          avatar_url?: string | null\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          email?: string\n          full_name?: string | null\n          phone?: string | null\n          date_of_birth?: string | null\n          address?: string | null\n          emergency_contact?: string | null\n          medical_history?: string | null\n          allergies?: string | null\n          avatar_url?: string | null\n        }\n      }\n      appointments: {\n        Row: {\n          id: string\n          created_at: string\n          updated_at: string\n          user_id: string | null\n          name: string\n          email: string\n          phone: string\n          service: string\n          preferred_date: string\n          preferred_time: string\n          appointment_date: string | null\n          duration: number | null\n          price: number | null\n          message: string | null\n          status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'\n          priority: 'low' | 'normal' | 'high' | 'urgent'\n          payment_status: 'unpaid' | 'partial' | 'paid' | 'refunded'\n          payment_amount: number\n          notes: string | null\n          reminder_sent: boolean\n          confirmed_at: string | null\n          completed_at: string | null\n          cancelled_at: string | null\n          cancellation_reason: string | null\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          user_id?: string | null\n          name: string\n          email: string\n          phone: string\n          service: string\n          preferred_date: string\n          preferred_time: string\n          appointment_date?: string | null\n          duration?: number | null\n          price?: number | null\n          message?: string | null\n          status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          payment_status?: 'unpaid' | 'partial' | 'paid' | 'refunded'\n          payment_amount?: number\n          notes?: string | null\n          reminder_sent?: boolean\n          confirmed_at?: string | null\n          completed_at?: string | null\n          cancelled_at?: string | null\n          cancellation_reason?: string | null\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          user_id?: string | null\n          name?: string\n          email?: string\n          phone?: string\n          service?: string\n          preferred_date?: string\n          preferred_time?: string\n          appointment_date?: string | null\n          duration?: number | null\n          price?: number | null\n          message?: string | null\n          status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'\n          priority?: 'low' | 'normal' | 'high' | 'urgent'\n          payment_status?: 'unpaid' | 'partial' | 'paid' | 'refunded'\n          payment_amount?: number\n          notes?: string | null\n          reminder_sent?: boolean\n          confirmed_at?: string | null\n          completed_at?: string | null\n          cancelled_at?: string | null\n          cancellation_reason?: string | null\n        }\n      }\n      services: {\n        Row: {\n          id: string\n          created_at: string\n          title: string\n          description: string\n          price: number\n          duration: string\n          benefit: string\n          image_url: string | null\n          is_popular: boolean\n          is_active: boolean\n          category: string\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          title: string\n          description: string\n          price: number\n          duration: string\n          benefit: string\n          image_url?: string | null\n          is_popular?: boolean\n          is_active?: boolean\n          category: string\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          title?: string\n          description?: string\n          price?: number\n          duration?: string\n          benefit?: string\n          image_url?: string | null\n          is_popular?: boolean\n          is_active?: boolean\n          category?: string\n        }\n      }\n      contact_forms: {\n        Row: {\n          id: string\n          created_at: string\n          name: string\n          email: string\n          phone: string | null\n          subject: string\n          message: string\n          status: 'new' | 'replied' | 'resolved'\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          name: string\n          email: string\n          phone?: string | null\n          subject: string\n          message: string\n          status?: 'new' | 'replied' | 'resolved'\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          name?: string\n          email?: string\n          phone?: string | null\n          subject?: string\n          message?: string\n          status?: 'new' | 'replied' | 'resolved'\n        }\n      }\n      testimonials: {\n        Row: {\n          id: string\n          created_at: string\n          name: string\n          title: string\n          quote: string\n          rating: number | null\n          avatar_url: string | null\n          is_featured: boolean\n          is_approved: boolean\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          name: string\n          title: string\n          quote: string\n          rating?: number | null\n          avatar_url?: string | null\n          is_featured?: boolean\n          is_approved?: boolean\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          name?: string\n          title?: string\n          quote?: string\n          rating?: number | null\n          avatar_url?: string | null\n          is_featured?: boolean\n          is_approved?: boolean\n        }\n      }\n      newsletter_subscribers: {\n        Row: {\n          id: string\n          created_at: string\n          email: string\n          name: string | null\n          is_active: boolean\n          subscribed_at: string\n          unsubscribed_at: string | null\n          source: string\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          email: string\n          name?: string | null\n          is_active?: boolean\n          subscribed_at?: string\n          unsubscribed_at?: string | null\n          source?: string\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          email?: string\n          name?: string | null\n          is_active?: boolean\n          subscribed_at?: string\n          unsubscribed_at?: string | null\n          source?: string\n        }\n      }\n      medical_records: {\n        Row: {\n          id: string\n          created_at: string\n          updated_at: string\n          patient_id: string\n          appointment_id: string | null\n          record_type: 'consultation' | 'treatment' | 'prescription' | 'lab_result' | 'x_ray' | 'diagnosis' | 'follow_up'\n          title: string\n          description: string | null\n          diagnosis: string | null\n          treatment: string | null\n          medications: any | null\n          vital_signs: any | null\n          notes: string | null\n          attachments: any | null\n          created_by: string | null\n          is_confidential: boolean\n          tags: string[] | null\n        }\n        Insert: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          patient_id: string\n          appointment_id?: string | null\n          record_type: 'consultation' | 'treatment' | 'prescription' | 'lab_result' | 'x_ray' | 'diagnosis' | 'follow_up'\n          title: string\n          description?: string | null\n          diagnosis?: string | null\n          treatment?: string | null\n          medications?: any | null\n          vital_signs?: any | null\n          notes?: string | null\n          attachments?: any | null\n          created_by?: string | null\n          is_confidential?: boolean\n          tags?: string[] | null\n        }\n        Update: {\n          id?: string\n          created_at?: string\n          updated_at?: string\n          patient_id?: string\n          appointment_id?: string | null\n          record_type?: 'consultation' | 'treatment' | 'prescription' | 'lab_result' | 'x_ray' | 'diagnosis' | 'follow_up'\n          title?: string\n          description?: string | null\n          diagnosis?: string | null\n          treatment?: string | null\n          medications?: any | null\n          vital_signs?: any | null\n          notes?: string | null\n          attachments?: any | null\n          created_by?: string | null\n          is_confidential?: boolean\n          tags?: string[] | null\n        }\n      }\n    }\n  }\n}\n\nexport type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']\n\n// Type aliases for easier use\nexport type Patient = Tables<'user_profiles'> & {\n  // Medical information from appointments\n  medical_history?: string\n  allergies?: string\n  current_medications?: string\n  previous_dental_work?: string\n  dental_concerns?: string\n  emergency_contact?: string\n  emergency_contact_phone?: string\n  emergency_contact_relationship?: string\n  has_insurance?: string\n  insurance_provider?: string\n  insurance_policy_number?: string\n  last_visit?: string\n\n  // Appointment statistics\n  total_appointments?: number\n  completed_appointments?: number\n  pending_appointments?: number\n  confirmed_appointments?: number\n  cancelled_appointments?: number\n}\nexport type Appointment = Tables<'appointments'>\nexport type Service = Tables<'services'>\nexport type ContactForm = Tables<'contact_forms'>\nexport type Testimonial = Tables<'testimonials'>\nexport type MedicalRecord = Tables<'medical_records'>\n"], "names": [], "mappings": ";;;;AAGoB;AAFpB;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,IAAA,oMAAmB,EAAC,aAAa", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/contexts/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n\n        // Only redirect on sign out, not on sign in\n        // This prevents automatic redirects when navigating between pages\n        if (event === 'SIGNED_OUT') {\n          router.push('/login')\n          router.refresh()\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router])\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    loading,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;AACA;;;AALA;;;;AAaA,MAAM,4BAAc,IAAA,8KAAa,EAA8B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,SAAS,IAAA,kJAAS;IAExB,IAAA,0KAAS;kCAAC;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,qIAAQ,CAAC,IAAI,CAAC,UAAU;wBACpD;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,qIAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;wBACJ;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;oBAEX,4CAA4C;oBAC5C,kEAAkE;oBAClE,IAAI,UAAU,cAAc;wBAC1B,OAAO,IAAI,CAAC;wBACZ,OAAO,OAAO;oBAChB;gBACF;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG;QAAC;KAAO;IAEX,MAAM,UAAU;QACd,MAAM,qIAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAhDgB;;QAGC,kJAAS;;;KAHV;AAkDT,SAAS;;IACd,MAAM,UAAU,IAAA,2KAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Bell, Search, <PERSON>u, <PERSON>, <PERSON>, <PERSON>r, <PERSON><PERSON><PERSON>, LogOut } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useTheme } from 'next-themes'\nimport { useAuth } from '@/contexts/auth-context'\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function Header({ onMenuClick }: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const { theme, setTheme } = useTheme()\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  return (\n    <header className=\"flex h-16 items-center justify-between border-b border-gray-200 dark:border-[#404040] bg-white dark:bg-[#1a1a1a] px-6 shadow-sm\">\n      {/* Left side */}\n      <div className=\"flex items-center gap-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"md:hidden text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-[#2a2a2a]\"\n          onClick={onMenuClick}\n        >\n          <Menu className=\"h-5 w-5\" />\n        </Button>\n\n        <div className=\"relative w-96 max-w-sm\">\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n          <Input\n            placeholder=\"Search patients, appointments...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"pl-10 bg-gray-50 dark:bg-[#2a2a2a] border-gray-200 dark:border-[#404040] text-gray-900 dark:text-gray-100 placeholder:text-gray-500 focus:ring-orange-500 focus:border-orange-500\"\n          />\n        </div>\n      </div>\n\n      {/* Right side */}\n      <div className=\"flex items-center gap-4\">\n        {/* Theme toggle */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n\n        {/* Notifications */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-4 w-4\" />\n              <Badge className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs\">\n                3\n              </Badge>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\" className=\"w-80\">\n            <DropdownMenuLabel>Notifications</DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <div className=\"space-y-2 p-2\">\n              <div className=\"flex items-start gap-3 rounded-lg p-2 hover:bg-accent\">\n                <div className=\"h-2 w-2 rounded-full bg-blue-500 mt-2\" />\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">New appointment request</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    Priya Sharma requested an appointment for tomorrow\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">2 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3 rounded-lg p-2 hover:bg-accent\">\n                <div className=\"h-2 w-2 rounded-full bg-green-500 mt-2\" />\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">Payment received</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    ₹15,000 payment received from Rajesh Kumar\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">1 hour ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3 rounded-lg p-2 hover:bg-accent\">\n                <div className=\"h-2 w-2 rounded-full bg-orange-500 mt-2\" />\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">Appointment reminder</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    Send reminder to Anita Patel for tomorrow&apos;s appointment\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">3 hours ago</p>\n                </div>\n              </div>\n            </div>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem className=\"text-center\">\n              View all notifications\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n\n        {/* User menu */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium\">\n                A\n              </div>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n            <DropdownMenuLabel className=\"font-normal\">\n              <div className=\"flex flex-col space-y-1\">\n                <p className=\"text-sm font-medium leading-none\">\n                  {user?.user_metadata?.full_name || 'Admin User'}\n                </p>\n                <p className=\"text-xs leading-none text-muted-foreground\">\n                  {user?.email || '<EMAIL>'}\n                </p>\n              </div>\n            </DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Profile</span>\n            </DropdownMenuItem>\n            <DropdownMenuItem>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              <span>Settings</span>\n            </DropdownMenuItem>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem onClick={handleSignOut} className=\"text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400\">\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              <span>Log out</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAQA;AACA;;;AAhBA;;;;;;;;;AAsBO,SAAS,OAAO,KAA4B;QAA5B,EAAE,WAAW,EAAe,GAA5B;QAmHJ;;IAlHjB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAA,+JAAQ;IACpC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,iJAAO;IAEjC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,6MAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mNAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,6IAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;;0CAErD,6LAAC,0MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC,6MAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAI5B,6LAAC,+JAAY;;0CACX,6LAAC,sKAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,+IAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,6LAAC,6MAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC,6IAAK;4CAAC,WAAU;sDAA4D;;;;;;;;;;;;;;;;;0CAKjF,6LAAC,sKAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,6LAAC,oKAAiB;kDAAC;;;;;;kDACnB,6LAAC,wKAAqB;;;;;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;kDAInD,6LAAC,wKAAqB;;;;;kDACtB,6LAAC,mKAAgB;wCAAC,WAAU;kDAAc;;;;;;;;;;;;;;;;;;kCAO9C,6LAAC,+JAAY;;0CACX,6LAAC,sKAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,+IAAM;oCAAC,SAAQ;oCAAQ,WAAU;8CAChC,cAAA,6LAAC;wCAAI,WAAU;kDAA+G;;;;;;;;;;;;;;;;0CAKlI,6LAAC,sKAAmB;gCAAC,WAAU;gCAAO,OAAM;gCAAM,UAAU;;kDAC1D,6LAAC,oKAAiB;wCAAC,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,CAAA,iBAAA,4BAAA,sBAAA,KAAM,aAAa,cAAnB,0CAAA,oBAAqB,SAAS,KAAI;;;;;;8DAErC,6LAAC;oDAAE,WAAU;8DACV,CAAA,iBAAA,2BAAA,KAAM,KAAK,KAAI;;;;;;;;;;;;;;;;;kDAItB,6LAAC,wKAAqB;;;;;kDACtB,6LAAC,mKAAgB;;0DACf,6LAAC,6MAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,mKAAgB;;0DACf,6LAAC,yNAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,wKAAqB;;;;;kDACtB,6LAAC,mKAAgB;wCAAC,SAAS;wCAAe,WAAU;;0DAClD,6LAAC,uNAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GA7IgB;;QAEc,+JAAQ;QACV,iJAAO;;;KAHnB", "debugId": null}}, {"offset": {"line": 1611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/auth/auth-guard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Loader2 } from 'lucide-react'\n\ninterface AuthGuardProps {\n  children: React.ReactNode\n}\n\nexport function AuthGuard({ children }: AuthGuardProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-16 w-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg mb-4\">\n            <Loader2 className=\"h-8 w-8 text-white animate-spin\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Loading...\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Checking authentication status\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show nothing while redirecting to login\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-16 w-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg mb-4\">\n            <Loader2 className=\"h-8 w-8 text-white animate-spin\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Redirecting...\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Please wait while we redirect you to login\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  // User is authenticated, render children\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;;IACxB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,iJAAO;IACjC,MAAM,SAAS,IAAA,kJAAS;IAExB,IAAA,0KAAS;+BAAC;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+NAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,0CAA0C;IAC1C,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+NAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,yCAAyC;IACzC,qBAAO;kBAAG;;AACZ;GAlDgB;;QACY,iJAAO;QAClB,kJAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { usePathname } from 'next/navigation'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { AuthGuard } from '@/components/auth/auth-guard'\nimport { cn } from '@/lib/utils'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n\n  // Don't show auth guard on login page\n  if (pathname === '/login') {\n    return <>{children}</>\n  }\n\n  return (\n    <AuthGuard>\n    <div className=\"flex h-screen bg-gray-50 dark:bg-[#0f0f0f]\">\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 md:hidden\">\n          <div\n            className=\"absolute inset-0 bg-black/60 backdrop-blur-sm\"\n            onClick={() => setSidebarOpen(false)}\n          />\n          <div className=\"absolute left-0 top-0 h-full shadow-2xl\">\n            <Sidebar />\n          </div>\n        </div>\n      )}\n\n      {/* Main content */}\n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-[#0f0f0f] p-6\">\n          <div className=\"mx-auto max-w-7xl\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n    </AuthGuard>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAaO,SAAS,WAAW,KAA6B;QAA7B,EAAE,QAAQ,EAAmB,GAA7B;;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,WAAW,IAAA,oJAAW;IAE5B,sCAAsC;IACtC,IAAI,aAAa,UAAU;QACzB,qBAAO;sBAAG;;IACZ;IAEA,qBACE,6LAAC,2JAAS;kBACV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qJAAO;;;;;;;;;;gBAIT,6BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,eAAe;;;;;;sCAEhC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qJAAO;;;;;;;;;;;;;;;;8BAMd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mJAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAC1C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOb;GA1CgB;;QAEG,oJAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Dentist%20website/admin-facing/src/components/theme-wrapper.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ThemeWrapperProps {\n  children: React.ReactNode\n}\n\nexport function ThemeWrapper({ children }: ThemeWrapperProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    // Return a loading state that matches the expected structure\n    return (\n      <div className=\"flex h-screen bg-[#0f0f0f]\">\n        <div className=\"flex h-full w-64 flex-col bg-[#171717] border-r border-[#404040]\">\n          <div className=\"flex h-16 items-center border-b border-[#404040] px-6\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600\">\n                <div className=\"h-5 w-5 bg-white rounded-sm\" />\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-base font-bold text-white\">DentalCare</span>\n                <span className=\"text-xs text-gray-400\">Loading...</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex flex-1 flex-col\">\n          <div className=\"flex h-16 items-center justify-between border-b border-[#404040] bg-[#1a1a1a] px-6\">\n            <div className=\"h-4 w-32 bg-gray-700 rounded animate-pulse\" />\n          </div>\n          <div className=\"flex-1 bg-[#0f0f0f] p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"h-8 w-48 bg-gray-700 rounded animate-pulse\" />\n              <div className=\"h-4 w-96 bg-gray-700 rounded animate-pulse\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAQO,SAAS,aAAa,KAA+B;QAA/B,EAAE,QAAQ,EAAqB,GAA/B;;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;kCAAC;YACR,WAAW;QACb;iCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,6DAA6D;QAC7D,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;sDACjD,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBAAO;kBAAG;;AACZ;GAxCgB;KAAA", "debugId": null}}]}